# ==================== 文件路径配置 ====================
# 输入文件路径
INPUT_FILE=debug_content.txt

# 输出文件路径
OUTPUT_FILE=filtered_resources.txt

# 资源JSON文件路径
RESOURCE_FILE=filtered_resources.txt.json

# 调试文件路径
DEBUG_HTML_FILE=debug_page.html
DEBUG_CONTENT_FILE=debug_content.txt

# ==================== 资源提取配置 ====================
# 过滤关键词（逗号分隔）
FILTER_KEYWORDS=短剧,软件

# 是否导出JSON格式
EXPORT_JSON=true

# 标题长度限制
MIN_TITLE_LENGTH=4
MAX_TITLE_LENGTH=200

# 无效标题标记（逗号分隔）
INVALID_TITLE_MARKERS=●,🔍,🆘

# 排除的标题列表（逗号分隔）
EXCLUDED_TITLES=美团，饿了么 红包每日领取,热门资源,夸克迅雷

# 标题排除词汇（逗号分隔）
TITLE_EXCLUDE_WORDS=搜索方法,注意,红包,优惠,登录,点击,输入,获取

# 标题关键词（逗号分隔）
TITLE_KEYWORDS=4K,高清,更新至,第,季,集,电影,电视剧,动漫,高码,蓝光,超前

# ==================== Telegram配置 ====================
# Telegram API配置 - 从 https://my.telegram.org 获取
TG_API_ID=your_api_id
TG_API_HASH=your_api_hash
TG_STRING_SESSION=your_string_session

# 目标频道配置
TARGET_CHANNEL=@your_channel_username
# 或者使用频道ID: TARGET_CHANNEL=-1001234567890

# 发送延迟（秒）
SEND_DELAY=3.0

# 去重功能配置
ENABLE_DEDUPLICATION=true
DEDUP_CACHE_FILE=sent_resources_cache.json
DEDUP_DAYS_TO_KEEP=7

# ==================== 金山文档抓取配置 ====================
# 金山文档URL
KDOCS_URL=https://www.kdocs.cn/l/caRody46qOEl

# 请求配置
REQUEST_TIMEOUT=30
MIN_WAIT_MS=1000
MAX_WAIT_MS=3000
MIN_CONTENT_LENGTH=100

# 用户代理
USER_AGENT=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36

# ==================== URL模式配置 ====================
# URL匹配模式（用|分隔，留空使用默认模式）
URL_PATTERNS=

# ==================== 日志配置 ====================
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO

# 日志文件路径（留空则只输出到控制台）
LOG_FILE=

# 日志格式
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_DATE_FORMAT=%Y-%m-%d %H:%M:%S
