#!/usr/bin/env python3
"""
简单测试脚本 - 快速验证爬取功能
"""

import asyncio
from scraper import KDocsScraper
from config import Config

async def simple_test():
    """简单测试"""
    print("🚀 开始简单测试...")
    
    try:
        async with KDocsScraper() as scraper:
            print(f"📄 正在访问: {Config.KDOCS_URL}")
            items = await scraper.scrape_document(Config.KDOCS_URL)
            
            print(f"✅ 爬取完成！")
            print(f"📊 结果: 共 {len(items)} 条内容")
            
            if items:
                print(f"\n📝 内容预览:")
                for i, item in enumerate(items[:10], 1):  # 只显示前10条
                    text = item.get('text', '')
                    category = item.get('category', '未知')
                    links = item.get('links', [])
                    
                    print(f"\n[{i}] 分类: {category}")
                    print(f"    内容: {text[:100]}{'...' if len(text) > 100 else ''}")
                    if links:
                        print(f"    链接: {len(links)} 个")
                        for link in links[:2]:
                            print(f"      - {link.get('name', '未知')}: {link.get('url', '')[:50]}...")
                    else:
                        print(f"    链接: 无")
            else:
                print(f"\n⚠️ 没有提取到内容")
                print(f"💡 建议运行 python analyze_page.py 分析页面结构")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(simple_test())
