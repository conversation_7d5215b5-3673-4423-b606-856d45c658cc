import requests
import re
from bs4 import BeautifulSoup
import random
import time
from datetime import timedelta
from config import config
from logger import get_logger
from performance import cached, timed, performance_monitor
from exceptions import NetworkError, ContentParsingError

# 获取模块日志器
logger = get_logger(__name__)

class KDocsContentTester:
    def __init__(self):
        # 从配置获取URL匹配模式
        self.url_patterns = config.url_patterns

        # 请求头
        self.headers = {
            'User-Agent': config.user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def random_wait(self, min_ms=None, max_ms=None):
        """随机等待"""
        if min_ms is None:
            min_ms = config.min_wait_ms
        if max_ms is None:
            max_ms = config.max_wait_ms

        min_sec = min_ms / 1000
        max_sec = max_ms / 1000
        wait_time = random.uniform(min_sec, max_sec)
        time.sleep(wait_time)

    def identify_platform(self, url):
        """识别网盘平台"""
        platform_map = config.platform_mapping

        for key, value in platform_map.items():
            if key in url:
                return value
        return '其他'

    @cached(ttl=timedelta(hours=1), use_file_cache=True)
    @timed(performance_monitor)
    def fetch_kdocs_content(self, url):
        """获取金山文档内容（带缓存和性能监控）"""
        try:
            logger.info(f"开始获取金山文档内容: {url}")

            # 添加随机延迟避免被限制
            self.random_wait()

            # 发送请求
            response = requests.get(url, headers=self.headers, timeout=config.request_timeout)
            response.raise_for_status()
            response.encoding = 'utf-8'

            logger.info(f"成功获取响应，状态码: {response.status_code}，内容长度: {len(response.text)}")

            # 保存原始HTML用于调试
            with open(config.debug_html_file, 'w', encoding='utf-8') as f:
                f.write(response.text)

            soup = BeautifulSoup(response.text, 'html.parser')

            # 尝试多种方式提取内容
            content_methods = [
                # 方法1: 查找特定的内容容器
                lambda: self.extract_from_containers(soup),
                # 方法2: 查找所有链接及其上下文
                lambda: self.extract_from_links(soup),
                # 方法3: 从页面文本中提取
                lambda: soup.get_text()
            ]

            for i, method in enumerate(content_methods, 1):
                try:
                    content = method()
                    if content and len(content.strip()) > config.min_content_length:  # 确保内容不为空
                        logger.info(f"使用方法 {i} 成功提取内容，长度: {len(content)}")
                        return content
                except Exception as e:
                    logger.debug(f"方法 {i} 失败: {e}")
                    continue

            logger.warning("所有提取方法都失败了")
            return None

        except requests.RequestException as e:
            logger.error(f"网络请求失败 {url}: {e}")
            raise NetworkError(f"获取金山文档失败: {url}", original_error=e)
        except Exception as e:
            logger.error(f"获取文档内容失败 {url}: {e}", exc_info=True)
            raise ContentParsingError(f"解析文档内容失败: {url}", original_error=e)

    def extract_from_containers(self, soup):
        """从特定容器中提取内容"""
        # 常见的内容容器选择器
        selectors = [
            'div[class*="content"]',
            'div[class*="document"]',
            'div[class*="editor"]',
            'div[class*="text"]',
            'main',
            'article',
            '.content',
            '.document-content',
            '.editor-content'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                content = '\n'.join([elem.get_text() for elem in elements])
                if len(content.strip()) > config.min_content_length:
                    return content

        return None

    def extract_from_links(self, soup):
        """从链接及其上下文中提取内容"""
        content_parts = []

        # 查找所有包含网盘链接的元素
        for pattern in self.url_patterns:
            # 在HTML中查找匹配的链接
            matches = re.finditer(pattern, str(soup))
            for match in matches:
                link = match.group(0)
                # 尝试找到包含这个链接的元素
                link_elements = soup.find_all('a', href=re.compile(re.escape(link)))
                for elem in link_elements:
                    # 获取父元素的文本作为上下文
                    parent = elem.parent
                    if parent:
                        context = parent.get_text().strip()
                        if context:
                            content_parts.append(context)

        return '\n'.join(content_parts) if content_parts else None

    @timed(performance_monitor)
    def parse_kdocs_content(self, content):
        """解析金山文档内容，提取标题和链接（带性能监控）"""
        logger.info("开始解析金山文档内容")
        results = []
        lines = content.strip().split('\n')

        # 预处理：找到所有可能的标题行
        title_candidates = []
        link_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 检查是否包含链接
            has_link = any(re.search(pattern, line) for pattern in self.url_patterns)

            if has_link:
                link_lines.append((i, line))
            elif self.is_likely_title(line):
                title_candidates.append((i, line))

        # 为每个链接行找到最近的标题
        for link_line_idx, link_line in link_lines:
            # 提取链接
            links_found = []
            for pattern in self.url_patterns:
                matches = re.findall(pattern, link_line)
                for match in matches:
                    platform = self.identify_platform(match)
                    links_found.append({
                        'url': match,
                        'platform': platform
                    })

            # 找到最近的标题（向前查找）
            best_title = "Unknown"
            min_distance = float('inf')

            for title_idx, title_line in title_candidates:
                if title_idx < link_line_idx:  # 标题应该在链接之前
                    distance = link_line_idx - title_idx
                    if distance < min_distance:
                        min_distance = distance
                        best_title = title_line

            # 如果没找到合适的标题，尝试从附近的行中提取
            if best_title == "Unknown" or min_distance > 10:
                best_title = self.extract_title_from_nearby_lines(lines, link_line_idx)

            # 为每个链接创建结果
            for link in links_found:
                results.append({
                    'title': best_title,
                    'url': link['url'],
                    'platform': link['platform'],
                    'message': f"**{best_title}**\n\n{link['platform']}: {link['url']}"
                })

        return results

    def is_likely_title(self, line):
        """判断一行是否可能是标题"""
        # 过滤掉明显不是标题的行
        if len(line) < config.min_title_length or len(line) > config.max_title_length:
            return False

        # 排除包含特定词汇的行
        if any(word in line for word in config.title_exclude_words):
            return False

        # 包含影视相关关键词的更可能是标题
        if any(keyword in line for keyword in config.title_keywords):
            return True

        # 包含年份的可能是标题
        if re.search(r'\[?\d{4}\]?', line):
            return True

        # 包含中文且长度适中的可能是标题
        if re.search(r'[\u4e00-\u9fff]', line) and 5 <= len(line) <= 100:
            return True

        return False

    def extract_title_from_nearby_lines(self, lines, current_index):
        """从附近的行中提取标题"""
        # 向前查找标题（最多查找10行）
        for i in range(max(0, current_index - 10), current_index):
            line = lines[i].strip()
            if self.is_likely_title(line):
                return line

        # 如果向前没找到，向后查找（最多查找3行）
        for i in range(current_index + 1, min(len(lines), current_index + 4)):
            line = lines[i].strip()
            if self.is_likely_title(line):
                return line

        return "Unknown"

def test_fetch_content():
    """测试金山文档内容获取"""

    # 创建测试实例
    tester = KDocsContentTester()

    # 从配置获取测试URL
    test_url = config.kdocs_url

    logger.info(f"正在获取页面内容: {test_url}")
    content = tester.fetch_kdocs_content(test_url)

    if content:
        logger.info(f"成功获取内容，长度: {len(content)}")
        logger.info("前500个字符:")
        logger.info(content[:500])
        logger.info("="*50)

        # 测试解析
        logger.info("开始解析资源...")
        resources = tester.parse_kdocs_content(content)

        logger.info(f"解析到 {len(resources)} 个资源:")
        for i, resource in enumerate(resources[:10], 1):  # 只显示前10个
            logger.info(f"{i}. 【{resource['platform']}】{resource['title']}")
            logger.info(f"   链接: {resource['url']}")

        # 保存解析结果用于调试
        with open(config.debug_content_file, 'w', encoding='utf-8') as f:
            f.write(content)

        logger.info(f"原始内容已保存到 {config.debug_content_file}")
        logger.info(f"原始HTML已保存到 {config.debug_html_file}")

    else:
        logger.error("获取内容失败")

if __name__ == "__main__":
    test_fetch_content()
