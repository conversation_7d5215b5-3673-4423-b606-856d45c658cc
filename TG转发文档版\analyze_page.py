#!/usr/bin/env python3
"""
页面结构分析工具 - 帮助理解目标页面的DOM结构
"""

import asyncio
import logging
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup
from config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def analyze_page_structure():
    """分析页面结构"""
    print("🔍 分析页面结构...")
    print(f"📄 目标URL: {Config.KDOCS_URL}")
    print("-" * 60)
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=Config.HEADLESS)
        page = await browser.new_page()
        
        try:
            # 访问页面
            await page.goto(Config.KDOCS_URL, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(5000)
            
            # 获取页面内容
            content = await page.content()
            soup = BeautifulSoup(content, 'lxml')
            
            # 基本信息
            print(f"📋 页面标题: {soup.title.string if soup.title else '无标题'}")
            print(f"📏 HTML长度: {len(content)} 字符")
            
            # 分析DOM结构
            print(f"\n🏗️ DOM结构分析:")
            
            # 统计各种标签
            tag_counts = {}
            for tag in soup.find_all():
                tag_name = tag.name
                tag_counts[tag_name] = tag_counts.get(tag_name, 0) + 1
            
            print(f"📊 标签统计 (前10个):")
            sorted_tags = sorted(tag_counts.items(), key=lambda x: x[1], reverse=True)
            for tag, count in sorted_tags[:10]:
                print(f"  {tag}: {count} 个")
            
            # 查找可能的内容容器
            print(f"\n📦 可能的内容容器:")
            
            # 检查常见的内容容器
            containers = [
                ('div', 'class', ['content', 'document', 'doc-content', 'editor', 'main']),
                ('div', 'id', ['content', 'document', 'main', 'editor']),
                ('article', None, None),
                ('section', None, None),
            ]
            
            for tag_name, attr, values in containers:
                if attr and values:
                    for value in values:
                        elements = soup.find_all(tag_name, {attr: lambda x: x and value in str(x).lower()})
                        if elements:
                            print(f"  {tag_name}[{attr}*='{value}']: {len(elements)} 个")
                else:
                    elements = soup.find_all(tag_name)
                    if elements:
                        print(f"  {tag_name}: {len(elements)} 个")
            
            # 分析文本内容
            print(f"\n📝 文本内容分析:")
            
            # 获取所有文本
            all_texts = soup.find_all(text=True)
            visible_texts = [text.strip() for text in all_texts if text.strip()]
            
            print(f"总文本节点: {len(all_texts)}")
            print(f"可见文本节点: {len(visible_texts)}")
            
            # 按长度分类
            short_texts = [t for t in visible_texts if 5 <= len(t) <= 50]
            medium_texts = [t for t in visible_texts if 50 < len(t) <= 200]
            long_texts = [t for t in visible_texts if len(t) > 200]
            
            print(f"短文本 (5-50字符): {len(short_texts)}")
            print(f"中等文本 (50-200字符): {len(medium_texts)}")
            print(f"长文本 (>200字符): {len(long_texts)}")
            
            # 显示一些示例文本
            print(f"\n📄 文本示例:")
            sample_texts = visible_texts[:20]  # 前20个文本
            for i, text in enumerate(sample_texts, 1):
                if len(text) >= 10:  # 只显示有意义的文本
                    print(f"  [{i}] {text[:80]}{'...' if len(text) > 80 else ''}")
            
            # 查找链接
            print(f"\n🔗 链接分析:")
            links = soup.find_all('a', href=True)
            print(f"总链接数: {len(links)}")
            
            # 分析网盘链接
            netdisk_patterns = {
                '夸克': 'quark.cn',
                '迅雷': 'xunlei.com',
                '百度': 'baidu.com',
                '阿里': 'aliyundrive.com',
                '蓝奏': 'lanzou'
            }
            
            for name, pattern in netdisk_patterns.items():
                matching_links = [link for link in links if pattern in link.get('href', '')]
                if matching_links:
                    print(f"  {name}网盘: {len(matching_links)} 个")
                    for link in matching_links[:3]:  # 显示前3个
                        print(f"    - {link.get('href', '')[:60]}...")
            
            # 保存页面源码用于进一步分析
            with open('page_source.html', 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"\n💾 页面源码已保存到: page_source.html")
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            await browser.close()

async def test_selectors():
    """测试不同的选择器"""
    print(f"\n🎯 测试选择器效果...")
    
    async with async_playwright() as playwright:
        browser = await playwright.chromium.launch(headless=Config.HEADLESS)
        page = await browser.new_page()
        
        try:
            await page.goto(Config.KDOCS_URL, wait_until='networkidle', timeout=30000)
            await page.wait_for_timeout(5000)
            
            content = await page.content()
            soup = BeautifulSoup(content, 'lxml')
            
            # 测试各种选择器
            test_selectors = [
                'div[data-type="paragraph"]',
                'div.doc-content p',
                'div.editor-content p',
                'div.content p',
                'div.document p',
                'p',
                'div',
                'span',
                '[class*="content"]',
                '[class*="doc"]',
                '[class*="editor"]',
                '[id*="content"]',
                '[id*="doc"]',
            ]
            
            for selector in test_selectors:
                try:
                    elements = soup.select(selector)
                    if elements:
                        texts = [elem.get_text(strip=True) for elem in elements if elem.get_text(strip=True)]
                        meaningful_texts = [t for t in texts if len(t) >= 10]
                        print(f"  '{selector}': {len(elements)} 元素, {len(meaningful_texts)} 有意义文本")
                        
                        # 显示前3个有意义的文本
                        for i, text in enumerate(meaningful_texts[:3], 1):
                            print(f"    [{i}] {text[:60]}...")
                except Exception as e:
                    print(f"  '{selector}': 错误 - {e}")
            
        except Exception as e:
            print(f"❌ 选择器测试失败: {e}")
        
        finally:
            await browser.close()

if __name__ == "__main__":
    try:
        asyncio.run(analyze_page_structure())
        asyncio.run(test_selectors())
        print(f"\n🎉 分析完成！")
    except KeyboardInterrupt:
        print(f"\n👋 分析被用户中断")
    except Exception as e:
        print(f"\n💥 分析出错: {e}")
        import traceback
        traceback.print_exc()
