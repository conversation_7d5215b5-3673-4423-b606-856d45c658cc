from KDocsForwarder import KDocsForwarder

def test_kdocs_forwarder():
    """测试金山文档转发器的解析功能（不连接TG）"""
    
    # 创建一个测试实例（不初始化TG客户端）
    class TestKDocsForwarder(KDocsForwarder):
        def __init__(self):
            # 只初始化必要的属性，跳过TG客户端
            self.urls_kw = ['ed2k','magnet', 'drive.uc.cn', 'caiyun.139.com', 'cloud.189.cn', 
                           'pan.quark.cn', '115cdn.com','115.com', 'anxia.com', 'alipan.com', 
                           'aliyundrive.com','pan.baidu.com','mypikpak.com','123684.com',
                           '123685.com','123912.com','123pan.com','123pan.cn','123592.com',
                           'pan.xunlei.com']
            
            self.url_patterns = [
                r'https://pan\.quark\.cn/s/[a-zA-Z0-9]+',
                r'https://pan\.xunlei\.com/s/[a-zA-Z0-9_-]+\?pwd=[a-zA-Z0-9]+#?',
                r'https://pan\.baidu\.com/s/[a-zA-Z0-9_-]+',
                r'https://alipan\.com/s/[a-zA-Z0-9]+',
                r'https://aliyundrive\.com/s/[a-zA-Z0-9]+',
                r'magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s]*',
                r'ed2k://\|file\|[^|]+\|\d+\|[A-Fa-f0-9]+\|/?'
            ]
            
            self.include = ['4K', '高清', '更新至', '第', '季', '集', '电影', '电视剧', '动漫']
            self.exclude = ['红包', '优惠', '搜索', '注意', '登录']
            
            self.headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1',
            }
    
    # 创建测试实例
    forwarder = TestKDocsForwarder()
    
    # 测试URL
    test_url = "https://www.kdocs.cn/l/caRody46qOEl"
    
    print(f"正在测试金山文档转发器...")
    print(f"目标URL: {test_url}")
    print("="*60)
    
    # 1. 测试内容获取
    print("1. 测试内容获取...")
    content = forwarder.fetch_kdocs_content(test_url)
    
    if not content:
        print("❌ 内容获取失败")
        return
    
    print(f"✅ 内容获取成功，长度: {len(content)}")
    
    # 2. 测试资源解析
    print("\n2. 测试资源解析...")
    resources = forwarder.parse_kdocs_content(content)
    print(f"✅ 解析到 {len(resources)} 个资源")
    
    # 3. 测试过滤功能
    print("\n3. 测试过滤功能...")
    filtered_resources = []
    
    for resource in resources:
        # 检查是否符合包含条件
        if forwarder.contains(resource['title'], forwarder.include):
            # 检查是否包含排除词
            if forwarder.nocontains(resource['title'], forwarder.exclude):
                filtered_resources.append(resource)
    
    print(f"✅ 过滤后剩余 {len(filtered_resources)} 个资源")
    
    # 4. 显示结果
    print("\n4. 过滤后的资源列表:")
    print("-" * 60)
    
    for i, resource in enumerate(filtered_resources[:15], 1):  # 只显示前15个
        print(f"{i:2d}. 【{resource['platform']}】{resource['title']}")
        print(f"    链接: {resource['url']}")
        print()
    
    if len(filtered_resources) > 15:
        print(f"... 还有 {len(filtered_resources) - 15} 个资源")
    
    # 5. 分类统计
    print("\n5. 分类统计:")
    print("-" * 30)
    
    platforms = {}
    for resource in filtered_resources:
        platform = resource['platform']
        platforms[platform] = platforms.get(platform, 0) + 1
    
    for platform, count in sorted(platforms.items()):
        print(f"  {platform}: {count} 个")
    
    print(f"\n总计: {len(filtered_resources)} 个有效资源")
    print("="*60)
    print("✅ 测试完成！解析和过滤功能正常工作")

if __name__ == "__main__":
    test_kdocs_forwarder()
