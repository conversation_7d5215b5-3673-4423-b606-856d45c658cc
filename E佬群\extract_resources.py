#!/usr/bin/env python
# -*- coding: utf-8 -*-

import re
import sys
import json
from pathlib import Path
from typing import List, Optional
from datetime import timedelta
from config import config
from logger import get_logger
from performance import timed, performance_monitor, BatchProcessor
from exceptions import (
    FileProcessingError,
    ResourceExtractionError,
    ValidationError,
    handle_exceptions,
    ErrorContext
)
from models import Resource, ResourceLink, ExtractionResult

# 获取模块日志器
logger = get_logger(__name__)

@timed(performance_monitor)
def extract_resources(file_path, filter_keywords=None):
    """提取文档中的资源标题和对应的链接（带性能监控）

    Args:
        file_path: 文档路径
        filter_keywords: 需要过滤的关键词列表，标题中包含这些关键词的资源会被过滤掉

    Returns:
        ExtractionResult: 提取结果对象，包含资源列表和统计信息

    Raises:
        FileProcessingError: 文件处理错误
        ResourceExtractionError: 资源提取错误
    """
    # 验证输入参数
    if not file_path:
        raise ValidationError("文件路径不能为空")

    file_path = Path(file_path)
    if not file_path.exists():
        raise FileProcessingError(f"文件不存在: {file_path}")

    if not file_path.is_file():
        raise FileProcessingError(f"路径不是文件: {file_path}")

    try:
        # 读取文件内容
        with ErrorContext("读取文件内容", logger_name=__name__) as ctx:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            if not content.strip():
                raise FileProcessingError("文件内容为空")

        if ctx.exception:
            raise FileProcessingError("读取文件失败", original_error=ctx.exception)
        
        # 使用简单的方法来识别标题和链接
        with ErrorContext("解析文件内容", logger_name=__name__) as ctx:
            lines = content.split('\n')

            resources = []
            current_title = None
            current_links = []

            for line_num, line in enumerate(lines, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    # 如果是链接行
                    if line.startswith('http'):
                        if current_title:
                            current_links.append(line)
                    else:
                        # 保存前一个标题和链接
                        if current_title and current_links:
                            # 创建Resource对象
                            resource = Resource(
                                title=current_title,
                                links=[ResourceLink(url=url) for url in current_links]
                            )
                            resources.append(resource)

                        # 如果不是无效行，则认为是新标题
                        if not any(mark in line for mark in config.invalid_title_markers) and \
                           line not in config.excluded_titles and \
                           len(line) > config.min_title_length:
                            current_title = line
                            current_links = []

                except Exception as line_error:
                    logger.warning(f"处理第 {line_num} 行时出错: {str(line_error)}")
                    continue

            # 添加最后一个资源
            if current_title and current_links:
                resource = Resource(
                    title=current_title,
                    links=[ResourceLink(url=url) for url in current_links]
                )
                resources.append(resource)

        if ctx.exception:
            raise ResourceExtractionError("解析文件内容失败", original_error=ctx.exception)

        # 创建提取结果对象
        extraction_result = ExtractionResult(
            resources=resources,
            source_file=str(file_path),
            filter_keywords=filter_keywords or []
        )

        # 过滤资源
        if filter_keywords:
            with ErrorContext("过滤资源", logger_name=__name__) as filter_ctx:
                filtered_resources = []
                for resource in resources:
                    try:
                        if is_valid_title(resource.title, filter_keywords) and resource.links:
                            filtered_resources.append(resource)
                    except Exception as filter_error:
                        logger.warning(f"过滤资源时出错: {str(filter_error)}, 标题: {resource.title}")
                        continue

            if filter_ctx.exception:
                logger.warning(f"过滤过程中出现错误: {str(filter_ctx.exception)}")

            # 更新提取结果
            extraction_result.resources = filtered_resources
            extraction_result.filtered_count = len(resources) - len(filtered_resources)

        logger.info(f"成功提取 {len(extraction_result.resources)} 个有效资源（原始: {len(resources)} 个）")
        return extraction_result

    except (FileProcessingError, ResourceExtractionError, ValidationError):
        # 重新抛出业务异常
        raise
    except Exception as e:
        # 包装未预期的异常
        raise ResourceExtractionError("资源提取过程中发生未预期的错误", original_error=e)

def is_valid_title(title, filter_keywords=None):
    """判断标题是否有效

    Args:
        title: 标题文本
        filter_keywords: 需要过滤的关键词列表
    """
    # 过滤掉纯数字、分隔线或者特别短的标题
    if not title or len(title) < config.min_title_length:
        return False

    # 检查标题长度上限
    if len(title) > config.max_title_length:
        return False

    # 如果全是数字或分隔符，则无效
    if all(c.isdigit() or c in '-. ' for c in title):
        return False

    # 检查是否是分隔符行
    if all(c in '-=_+* ' for c in title):
        return False

    # 检查是否包含过滤关键词
    if filter_keywords:
        for keyword in filter_keywords:
            if keyword in title:
                return False

    return True

def print_results(resources):
    """打印提取的资源"""
    for item in resources:
        logger.info(f"标题: {item['title']}")
        logger.info("链接:")
        for link in item['links']:
            logger.info(f"  {link}")
        logger.info("-" * 50)

@handle_exceptions(
    exceptions=[IOError, OSError, PermissionError],
    log_error=True,
    custom_message="保存文件失败"
)
def save_to_file(extraction_result, output_file):
    """保存结果到文件

    Args:
        extraction_result: ExtractionResult对象或资源列表（向后兼容）
        output_file: 输出文件路径

    Raises:
        FileProcessingError: 文件保存失败
    """
    # 处理向后兼容性
    if isinstance(extraction_result, ExtractionResult):
        resources = extraction_result.resources
        total_count = len(resources)
    elif isinstance(extraction_result, list):
        # 旧格式：直接是资源列表
        resources = [Resource.from_dict(item) if isinstance(item, dict) else item
                    for item in extraction_result]
        total_count = len(resources)
    else:
        logger.warning("未知的资源格式")
        return

    if not resources:
        logger.warning("没有资源需要保存")
        return

    output_path = Path(output_file)

    # 确保输出目录存在
    with ErrorContext("创建输出目录", logger_name=__name__, reraise=False):
        output_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        with ErrorContext("写入文件", logger_name=__name__) as ctx:
            with open(output_path, 'w', encoding='utf-8') as f:
                for i, resource in enumerate(resources, 1):
                    try:
                        f.write(f"标题: {resource.title}\n")
                        f.write("链接:\n")
                        for link in resource.links:
                            if link.password:
                                f.write(f"  {link.url} (密码: {link.password})\n")
                            else:
                                f.write(f"  {link.url}\n")

                        # 添加额外信息
                        if resource.quality:
                            f.write(f"质量: {resource.quality}\n")
                        if resource.status:
                            f.write(f"状态: {resource.status}\n")
                        if resource.platforms:
                            f.write(f"平台: {', '.join(resource.platform_names)}\n")

                        f.write("-" * 50 + "\n")
                    except Exception as item_error:
                        logger.warning(f"写入第 {i} 个资源时出错: {str(item_error)}")
                        continue

                f.write(f"\n总共提取了 {total_count} 个资源项\n")

        if ctx.exception:
            raise FileProcessingError(f"写入文件失败: {output_file}", original_error=ctx.exception)

        logger.info(f"结果已保存到 {output_file}")

    except FileProcessingError:
        raise
    except Exception as e:
        raise FileProcessingError(f"保存文件时发生未预期的错误: {output_file}", original_error=e)

@handle_exceptions(
    exceptions=[IOError, OSError, PermissionError, json.JSONDecodeError],
    log_error=True,
    custom_message="导出JSON失败"
)
def export_json(extraction_result, json_file, legacy_format=False):
    """导出为JSON格式

    Args:
        extraction_result: ExtractionResult对象或资源列表（向后兼容）
        json_file: JSON文件路径
        legacy_format: 是否使用旧格式（向后兼容）

    Raises:
        FileProcessingError: JSON导出失败
    """
    # 处理向后兼容性
    if isinstance(extraction_result, ExtractionResult):
        if legacy_format:
            data = extraction_result.to_legacy_format()
        else:
            data = extraction_result.to_dict()
    elif isinstance(extraction_result, list):
        # 旧格式：直接是资源列表
        if legacy_format:
            data = extraction_result
        else:
            # 转换为新格式
            resources = [Resource.from_dict(item) if isinstance(item, dict) else item
                        for item in extraction_result]
            result = ExtractionResult(resources=resources)
            data = result.to_dict()
    else:
        logger.warning("未知的资源格式")
        return

    if not data:
        logger.warning("没有资源需要导出为JSON")
        return

    json_path = Path(json_file)

    # 确保输出目录存在
    with ErrorContext("创建JSON输出目录", logger_name=__name__, reraise=False):
        json_path.parent.mkdir(parents=True, exist_ok=True)

    try:
        with ErrorContext("导出JSON", logger_name=__name__) as ctx:
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        if ctx.exception:
            raise FileProcessingError(f"导出JSON失败: {json_file}", original_error=ctx.exception)

        format_type = "旧格式" if legacy_format else "新格式"
        logger.info(f"结果已保存为JSON格式({format_type})到 {json_file}")

    except FileProcessingError:
        raise
    except Exception as e:
        raise FileProcessingError(f"导出JSON时发生未预期的错误: {json_file}", original_error=e)

def main():
    # 从配置获取默认值
    input_file = config.input_file
    output_file = config.output_file
    export_json_format = config.export_json
    filter_keywords = config.filter_keywords.copy()
    
    # 如果提供了命令行参数，则使用命令行参数
    if len(sys.argv) > 1:
        # 显示帮助信息
        if sys.argv[1] in ["-h", "--help"]:
            help_text = """资源提取工具 - 提取文档中的资源标题和链接

用法: python extract_resources.py [选项]

选项:
  -h, --help            显示此帮助信息
  -i, --input FILE      指定输入文件 (默认: {})
  -o, --output FILE     指定输出文件 (默认: {})
  --no-json             不导出JSON格式
  -f, --filter KEYWORDS 设置过滤关键词，用逗号分隔 (默认: {})

示例:
  python extract_resources.py
  python extract_resources.py -i my_content.txt -o results.txt
  python extract_resources.py -f 短剧,软件,广告""".format(
                config.input_file,
                config.output_file,
                ','.join(config.filter_keywords)
            )
            print(help_text)
            sys.exit(0)
        
        # 解析命令行参数
        i = 1
        while i < len(sys.argv):
            if sys.argv[i] in ["-i", "--input"] and i + 1 < len(sys.argv):
                input_file = sys.argv[i + 1]
                i += 2
            elif sys.argv[i] in ["-o", "--output"] and i + 1 < len(sys.argv):
                output_file = sys.argv[i + 1]
                i += 2
            elif sys.argv[i] == "--no-json":
                export_json_format = False
                i += 1
            elif sys.argv[i] in ["-f", "--filter"] and i + 1 < len(sys.argv):
                filter_keywords = sys.argv[i + 1].split(',')
                i += 2
            else:
                i += 1
    
    logger.info(f"使用输入文件: {input_file}")
    logger.info(f"使用输出文件: {output_file}")
    logger.info(f"使用过滤关键词: {', '.join(filter_keywords)}")

    try:
        # 提取资源
        logger.info("开始提取资源...")
        extraction_result = extract_resources(input_file, filter_keywords)

        if not extraction_result.resources:
            logger.warning("没有提取到有效资源")
            sys.exit(0)  # 正常退出，因为可能是正常情况

        # 保存结果
        with ErrorContext("保存结果", logger_name=__name__) as save_ctx:
            save_to_file(extraction_result, output_file)

        if save_ctx.exception:
            logger.error("保存文件失败，但继续尝试导出JSON")

        # 如果需要，导出JSON格式
        if export_json_format:
            json_file = output_file + ".json"
            with ErrorContext("导出JSON", logger_name=__name__) as json_ctx:
                # 导出新格式，同时保持向后兼容
                export_json(extraction_result, json_file, legacy_format=True)

            if json_ctx.exception:
                logger.error("导出JSON失败")

        logger.info(f"处理完成！总共提取了 {len(extraction_result.resources)} 个资源项")

    except ValidationError as e:
        logger.error(f"输入验证失败: {str(e)}")
        sys.exit(1)
    except FileProcessingError as e:
        logger.error(f"文件处理失败: {str(e)}")
        sys.exit(1)
    except ResourceExtractionError as e:
        logger.error(f"资源提取失败: {str(e)}")
        sys.exit(1)
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行过程中发生未预期的错误: {str(e)}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main() 