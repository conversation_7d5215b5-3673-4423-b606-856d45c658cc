#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
去重模块
基于当天频道已发资源的链接+标题进行对比去重
"""

import hashlib
import json
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict, Set, Tuple, Optional
from dataclasses import dataclass, field
from telethon import TelegramClient
from telethon.tl.types import Message
from logger import get_logger
from performance import cached, timed, performance_monitor
from exceptions import TelegramError, ErrorContext

logger = get_logger(__name__)

@dataclass
class SentResource:
    """已发送资源记录"""
    title: str
    links: List[str]
    message_id: int
    sent_time: datetime
    hash_key: str = field(init=False)
    
    def __post_init__(self):
        """生成唯一哈希键"""
        self.hash_key = self.generate_hash()
    
    def generate_hash(self) -> str:
        """基于标题和链接生成唯一哈希"""
        # 标准化标题（去除空格、转小写）
        normalized_title = self.title.strip().lower()
        # 排序链接确保一致性
        sorted_links = sorted([link.strip() for link in self.links])
        
        # 组合标题和链接
        content = f"{normalized_title}|{'|'.join(sorted_links)}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'title': self.title,
            'links': self.links,
            'message_id': self.message_id,
            'sent_time': self.sent_time.isoformat(),
            'hash_key': self.hash_key
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'SentResource':
        """从字典创建实例"""
        return cls(
            title=data['title'],
            links=data['links'],
            message_id=data['message_id'],
            sent_time=datetime.fromisoformat(data['sent_time'])
        )

class DeduplicationManager:
    """去重管理器"""
    
    def __init__(self, cache_file: str = "sent_resources_cache.json"):
        self.cache_file = Path(cache_file)
        self.sent_resources: Dict[str, SentResource] = {}
        self.load_cache()
    
    def load_cache(self):
        """加载缓存文件"""
        if not self.cache_file.exists():
            logger.info("去重缓存文件不存在，将创建新文件")
            return
        
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 只加载今天的记录
            today = datetime.now().date()
            for item in data:
                resource = SentResource.from_dict(item)
                if resource.sent_time.date() == today:
                    self.sent_resources[resource.hash_key] = resource
            
            logger.info(f"加载了 {len(self.sent_resources)} 条今日已发送资源记录")
            
        except Exception as e:
            logger.warning(f"加载去重缓存失败: {str(e)}")
            self.sent_resources = {}
    
    def save_cache(self):
        """保存缓存文件"""
        try:
            # 只保存今天的记录
            today = datetime.now().date()
            today_resources = [
                resource.to_dict() 
                for resource in self.sent_resources.values()
                if resource.sent_time.date() == today
            ]
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(today_resources, f, ensure_ascii=False, indent=2)
            
            logger.debug(f"保存了 {len(today_resources)} 条去重缓存记录")
            
        except Exception as e:
            logger.warning(f"保存去重缓存失败: {str(e)}")
    
    def is_duplicate(self, title: str, links: List[str]) -> bool:
        """检查是否为重复资源"""
        temp_resource = SentResource(
            title=title,
            links=links,
            message_id=0,
            sent_time=datetime.now()
        )
        
        is_dup = temp_resource.hash_key in self.sent_resources
        if is_dup:
            existing = self.sent_resources[temp_resource.hash_key]
            logger.info(f"发现重复资源: {title[:50]}... (原发送时间: {existing.sent_time})")
        
        return is_dup
    
    def add_sent_resource(self, title: str, links: List[str], message_id: int):
        """添加已发送资源记录"""
        resource = SentResource(
            title=title,
            links=links,
            message_id=message_id,
            sent_time=datetime.now()
        )
        
        self.sent_resources[resource.hash_key] = resource
        logger.debug(f"添加已发送资源记录: {title[:50]}...")
    
    def get_duplicate_count(self) -> int:
        """获取今日重复资源数量"""
        return len(self.sent_resources)
    
    def cleanup_old_records(self, days_to_keep: int = 7):
        """清理旧记录"""
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        
        old_keys = [
            key for key, resource in self.sent_resources.items()
            if resource.sent_time < cutoff_date
        ]
        
        for key in old_keys:
            del self.sent_resources[key]
        
        if old_keys:
            logger.info(f"清理了 {len(old_keys)} 条过期记录")

class TelegramDeduplicator:
    """Telegram频道去重器"""
    
    def __init__(self, client: TelegramClient, channel_entity):
        self.client = client
        self.channel_entity = channel_entity
        self.dedup_manager = DeduplicationManager()
    
    @timed(performance_monitor)
    async def fetch_today_messages(self) -> List[SentResource]:
        """获取今日频道消息"""
        logger.info("正在获取今日频道消息用于去重...")

        # 使用UTC时间避免时区问题
        from datetime import timezone
        today_start = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
        sent_resources = []
        
        try:
            with ErrorContext("获取频道消息", logger_name=__name__) as ctx:
                # 获取今日消息
                async for message in self.client.iter_messages(
                    self.channel_entity,
                    offset_date=today_start,
                    reverse=True
                ):
                    if message.date < today_start:
                        break
                    
                    if message.text:
                        # 解析消息内容提取标题和链接
                        title, links = self._parse_message_content(message.text)
                        if title and links:
                            resource = SentResource(
                                title=title,
                                links=links,
                                message_id=message.id,
                                sent_time=message.date
                            )
                            sent_resources.append(resource)
            
            if ctx.exception:
                logger.warning(f"获取频道消息时出现错误: {str(ctx.exception)}")
            
            logger.info(f"获取到 {len(sent_resources)} 条今日已发送资源")
            
            # 完全以频道状态为准，清空本地缓存后重新填充
            self.dedup_manager.sent_resources.clear()
            for resource in sent_resources:
                self.dedup_manager.sent_resources[resource.hash_key] = resource

            logger.info(f"已同步频道状态，当前缓存 {len(self.dedup_manager.sent_resources)} 条记录")
            
            return sent_resources
            
        except Exception as e:
            logger.error(f"获取今日频道消息失败: {str(e)}")
            return []
    
    def _parse_message_content(self, text: str) -> Tuple[Optional[str], List[str]]:
        """解析消息内容提取标题和链接"""
        lines = text.strip().split('\n')
        title = None
        links = []

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检查是否是链接
            if line.startswith('http'):
                links.append(line)
            elif not title:
                # 提取标题，去除Markdown格式
                clean_title = line
                # 去除Markdown粗体标记
                if clean_title.startswith('**') and clean_title.endswith('**'):
                    clean_title = clean_title[2:-2]
                # 去除其他可能的格式标记
                clean_title = clean_title.strip('*_`')

                # 跳过表情符号开头的行
                if not clean_title.startswith(('📺', '🎬', '🎭', '🎪', '🎨', '🎯', '🎲')):
                    title = clean_title

        # 调试日志
        if title and links:
            logger.debug(f"解析消息 - 标题: {title[:30]}..., 链接数: {len(links)}")

        return title, links
    
    async def filter_duplicates(self, resources: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """过滤重复资源"""
        logger.info("开始去重检查...")
        
        # 先获取今日已发送的消息
        await self.fetch_today_messages()
        
        unique_resources = []
        duplicate_resources = []
        
        for resource in resources:
            title = resource.get('title', '')
            links = resource.get('links', [])
            
            if self.dedup_manager.is_duplicate(title, links):
                duplicate_resources.append(resource)
            else:
                unique_resources.append(resource)
        
        logger.info(f"去重结果: 总计 {len(resources)} 个资源")
        logger.info(f"  - 新资源: {len(unique_resources)} 个")
        logger.info(f"  - 重复资源: {len(duplicate_resources)} 个")
        
        return unique_resources, duplicate_resources
    
    def mark_as_sent(self, title: str, links: List[str], message_id: int):
        """标记资源为已发送"""
        self.dedup_manager.add_sent_resource(title, links, message_id)
        self.dedup_manager.save_cache()
    
    def get_stats(self) -> Dict:
        """获取去重统计信息"""
        return {
            'total_sent_today': len(self.dedup_manager.sent_resources),
            'cache_file': str(self.dedup_manager.cache_file),
            'last_updated': datetime.now().isoformat()
        }

# 便捷函数
async def create_deduplicator(client: TelegramClient, channel_entity) -> TelegramDeduplicator:
    """创建去重器实例"""
    return TelegramDeduplicator(client, channel_entity)
