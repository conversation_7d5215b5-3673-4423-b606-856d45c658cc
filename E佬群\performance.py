#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能优化模块
提供缓存、连接池、异步处理等性能优化功能
"""

import time
import hashlib
import pickle
import asyncio
from pathlib import Path
from typing import Any, Optional, Dict, Callable, Union, List
from functools import wraps
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import threading
from concurrent.futures import ThreadPoolExecutor
from logger import get_logger

logger = get_logger(__name__)

@dataclass
class CacheEntry:
    """缓存条目"""
    data: Any
    timestamp: datetime
    ttl: Optional[timedelta] = None
    access_count: int = 0
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return datetime.now() - self.timestamp > self.ttl
    
    def access(self) -> Any:
        """访问缓存数据"""
        self.access_count += 1
        return self.data


class MemoryCache:
    """内存缓存"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[timedelta] = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
    
    def _generate_key(self, key: Union[str, tuple, list]) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return key
        
        # 对复杂对象生成哈希键
        key_str = str(key)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: Union[str, tuple, list]) -> Optional[Any]:
        """获取缓存数据"""
        cache_key = self._generate_key(key)
        
        with self._lock:
            entry = self._cache.get(cache_key)
            if entry is None:
                return None
            
            if entry.is_expired():
                del self._cache[cache_key]
                return None
            
            return entry.access()
    
    def set(self, key: Union[str, tuple, list], value: Any, ttl: Optional[timedelta] = None) -> None:
        """设置缓存数据"""
        cache_key = self._generate_key(key)
        ttl = ttl or self.default_ttl
        
        with self._lock:
            # 如果缓存已满，删除最少使用的条目
            if len(self._cache) >= self.max_size and cache_key not in self._cache:
                self._evict_lru()
            
            self._cache[cache_key] = CacheEntry(
                data=value,
                timestamp=datetime.now(),
                ttl=ttl
            )
    
    def _evict_lru(self) -> None:
        """删除最少使用的条目"""
        if not self._cache:
            return
        
        # 找到访问次数最少的条目
        lru_key = min(self._cache.keys(), key=lambda k: self._cache[k].access_count)
        del self._cache[lru_key]
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self._cache)
    
    def cleanup_expired(self) -> int:
        """清理过期条目"""
        expired_keys = []
        
        with self._lock:
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._cache[key]
        
        return len(expired_keys)


class FileCache:
    """文件缓存"""
    
    def __init__(self, cache_dir: Union[str, Path] = ".cache", default_ttl: Optional[timedelta] = None):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.default_ttl = default_ttl
    
    def _get_cache_path(self, key: Union[str, tuple, list]) -> Path:
        """获取缓存文件路径"""
        if isinstance(key, str):
            cache_key = key
        else:
            cache_key = hashlib.md5(str(key).encode()).hexdigest()
        
        return self.cache_dir / f"{cache_key}.cache"
    
    def get(self, key: Union[str, tuple, list]) -> Optional[Any]:
        """获取缓存数据"""
        cache_path = self._get_cache_path(key)
        
        if not cache_path.exists():
            return None
        
        try:
            with open(cache_path, 'rb') as f:
                entry: CacheEntry = pickle.load(f)
            
            if entry.is_expired():
                cache_path.unlink(missing_ok=True)
                return None
            
            return entry.access()
            
        except Exception as e:
            logger.warning(f"读取缓存文件失败: {str(e)}")
            cache_path.unlink(missing_ok=True)
            return None
    
    def set(self, key: Union[str, tuple, list], value: Any, ttl: Optional[timedelta] = None) -> None:
        """设置缓存数据"""
        cache_path = self._get_cache_path(key)
        ttl = ttl or self.default_ttl
        
        entry = CacheEntry(
            data=value,
            timestamp=datetime.now(),
            ttl=ttl
        )
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(entry, f)
        except Exception as e:
            logger.warning(f"写入缓存文件失败: {str(e)}")
    
    def clear(self) -> None:
        """清空缓存"""
        for cache_file in self.cache_dir.glob("*.cache"):
            cache_file.unlink(missing_ok=True)
    
    def cleanup_expired(self) -> int:
        """清理过期文件"""
        expired_count = 0
        
        for cache_file in self.cache_dir.glob("*.cache"):
            try:
                with open(cache_file, 'rb') as f:
                    entry: CacheEntry = pickle.load(f)
                
                if entry.is_expired():
                    cache_file.unlink()
                    expired_count += 1
                    
            except Exception:
                # 损坏的缓存文件也删除
                cache_file.unlink(missing_ok=True)
                expired_count += 1
        
        return expired_count


# 全局缓存实例
memory_cache = MemoryCache(max_size=500, default_ttl=timedelta(hours=1))
file_cache = FileCache(default_ttl=timedelta(days=1))


def cached(ttl: Optional[timedelta] = None, 
          use_file_cache: bool = False,
          key_func: Optional[Callable] = None):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存过期时间
        use_file_cache: 是否使用文件缓存
        key_func: 自定义键生成函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = (func.__name__, args, tuple(sorted(kwargs.items())))
            
            # 选择缓存后端
            cache_backend = file_cache if use_file_cache else memory_cache
            
            # 尝试从缓存获取
            cached_result = cache_backend.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_backend.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {func.__name__}")
            
            return result
        
        return wrapper
    return decorator


def async_cached(ttl: Optional[timedelta] = None,
                use_file_cache: bool = False,
                key_func: Optional[Callable] = None):
    """
    异步缓存装饰器
    
    Args:
        ttl: 缓存过期时间
        use_file_cache: 是否使用文件缓存
        key_func: 自定义键生成函数
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = (func.__name__, args, tuple(sorted(kwargs.items())))
            
            # 选择缓存后端
            cache_backend = file_cache if use_file_cache else memory_cache
            
            # 尝试从缓存获取
            cached_result = cache_backend.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {func.__name__}")
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            cache_backend.set(cache_key, result, ttl)
            logger.debug(f"缓存设置: {func.__name__}")
            
            return result
        
        return wrapper
    return decorator


class BatchProcessor:
    """批处理器"""
    
    def __init__(self, batch_size: int = 10, max_workers: int = 4):
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
    
    def process_batch(self, items: List[Any], process_func: Callable) -> List[Any]:
        """批处理同步函数"""
        results = []
        
        # 分批处理
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            
            # 并行处理批次
            futures = [self.executor.submit(process_func, item) for item in batch]
            
            # 收集结果
            for future in futures:
                try:
                    result = future.result(timeout=30)
                    results.append(result)
                except Exception as e:
                    logger.warning(f"批处理项失败: {str(e)}")
                    results.append(None)
        
        return results
    
    async def async_process_batch(self, items: List[Any], process_func: Callable) -> List[Any]:
        """批处理异步函数"""
        results = []
        
        # 分批处理
        for i in range(0, len(items), self.batch_size):
            batch = items[i:i + self.batch_size]
            
            # 并行处理批次
            tasks = [process_func(item) for item in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for result in batch_results:
                if isinstance(result, Exception):
                    logger.warning(f"批处理项失败: {str(result)}")
                    results.append(None)
                else:
                    results.append(result)
        
        return results
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'executor'):
            self.executor.shutdown(wait=True)


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics: Dict[str, List[float]] = {}
        self._lock = threading.Lock()
    
    def record_time(self, operation: str, duration: float) -> None:
        """记录操作时间"""
        with self._lock:
            if operation not in self.metrics:
                self.metrics[operation] = []
            self.metrics[operation].append(duration)
    
    def get_stats(self, operation: str) -> Optional[Dict[str, float]]:
        """获取操作统计信息"""
        with self._lock:
            times = self.metrics.get(operation)
            if not times:
                return None
            
            return {
                'count': len(times),
                'total': sum(times),
                'average': sum(times) / len(times),
                'min': min(times),
                'max': max(times)
            }
    
    def get_all_stats(self) -> Dict[str, Dict[str, float]]:
        """获取所有操作统计信息"""
        return {op: self.get_stats(op) for op in self.metrics.keys()}


def timed(monitor: Optional[PerformanceMonitor] = None):
    """计时装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                if monitor:
                    monitor.record_time(func.__name__, duration)
                logger.debug(f"{func.__name__} 执行时间: {duration:.3f}秒")
        
        return wrapper
    return decorator


def async_timed(monitor: Optional[PerformanceMonitor] = None):
    """异步计时装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                if monitor:
                    monitor.record_time(func.__name__, duration)
                logger.debug(f"{func.__name__} 执行时间: {duration:.3f}秒")
        
        return wrapper
    return decorator


# 全局性能监控器
performance_monitor = PerformanceMonitor()
