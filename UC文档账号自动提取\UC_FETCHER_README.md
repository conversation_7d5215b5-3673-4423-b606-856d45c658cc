# UC账号密码获取脚本

## 功能说明
定时从语雀页面获取UC浏览器VIP账号密码信息，适合放入Windows计划任务中自动执行。

## 文件说明
- `uc_account_fetcher.py` - 主脚本文件（使用Playwright自动化浏览器）
- `uc_account.json` - 输出的JSON格式账号信息
- `uc_account.txt` - 输出的文本格式账号信息
- `uc_fetcher.log` - 运行日志文件

## 使用步骤

### 1. 安装依赖
```bash
pip install playwright
playwright install chromium
```

### 2. 手动测试
```bash
python uc_account_fetcher.py
```

### 3. 设置计划任务
1. 打开Windows任务计划程序
2. 创建基本任务
3. 设置触发器（如每小时执行一次）
4. 操作选择"启动程序"
5. 程序路径填写Python解释器路径
6. 添加参数: `uc_account_fetcher.py`
7. 起始于填写脚本所在目录

## 输出格式

### JSON格式 (uc_account.json)
```json
{
  "account": "************",
  "password": "uc112233",
  "update_time": "今天 15:34",
  "fetch_time": "2025-07-26 17:41:36",
  "source_url": "https://www.yuque.com/ganjuezijitaihaole/ovcqrm/ni0t2qna08dnw2q7?singleDoc#"
}
```

### 文本格式 (uc_account.txt)
```
UC浏览器VIP账号信息
==============================
账号: ************
密码: uc112233
页面更新时间: 今天 15:34
获取时间: 2025-07-26 17:41:36
来源: https://www.yuque.com/ganjuezijitaihaole/ovcqrm/ni0t2qna08dnw2q7?singleDoc#
```

## 注意事项
1. 确保网络连接正常
2. 如果页面结构发生变化，可能需要更新脚本中的提取逻辑
3. 建议设置合理的执行频率，避免过于频繁访问
4. 日志文件会记录详细的执行信息，便于排查问题

## 自定义配置
可以修改 `uc_account_fetcher.py` 中的以下参数：
- `output_file`: 输出文件名
- 日志级别和格式
- 浏览器启动参数
- 页面等待时间
