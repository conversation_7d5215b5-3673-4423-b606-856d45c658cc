# 环境配置文件
.env
*.env

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/

# Telegram 会话文件（已统一使用 .env 中的 STRING_SESSION）
# session.session
# *.session

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 测试文件
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# 数据文件
*.db
*.sqlite
*.sqlite3
data/
downloads/

# 备份文件
*.bak
*.backup
