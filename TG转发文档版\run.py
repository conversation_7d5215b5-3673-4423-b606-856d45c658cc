#!/usr/bin/env python3
"""
启动脚本 - 确保在正确目录运行主程序
"""

import os
import sys
import asyncio
from pathlib import Path

def main():
    """主函数"""
    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    
    # 切换到脚本目录
    original_cwd = os.getcwd()
    os.chdir(script_dir)
    
    try:
        print(f"📁 工作目录: {script_dir}")
        print("🚀 启动 TG 转发文档版...")
        
        # 导入并运行主程序
        from main import main as main_func
        asyncio.run(main_func())
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("请确保已安装所有依赖: python install.py")
    except Exception as e:
        print(f"💥 运行错误: {e}")
    finally:
        # 恢复原始工作目录
        os.chdir(original_cwd)

if __name__ == "__main__":
    main()
