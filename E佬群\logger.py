#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
统一日志系统模块
提供标准化的日志接口和配置
"""

import logging
import sys
from pathlib import Path
from typing import Optional
from config import config

class Logger:
    """统一日志管理类"""
    
    _loggers = {}  # 缓存已创建的logger
    _initialized = False
    
    @classmethod
    def setup_logging(cls, 
                     log_level: Optional[str] = None,
                     log_file: Optional[str] = None,
                     log_format: Optional[str] = None,
                     log_date_format: Optional[str] = None) -> None:
        """
        设置全局日志配置
        
        Args:
            log_level: 日志级别
            log_file: 日志文件路径
            log_format: 日志格式
            log_date_format: 日志日期格式
        """
        if cls._initialized:
            return
            
        # 使用配置或传入的参数
        level = log_level or config.log_level
        file_path = log_file or config.log_file
        format_str = log_format or config.log_format
        date_format = log_date_format or config.log_date_format
        
        # 设置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, level.upper(), logging.INFO))
        
        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 创建格式器
        formatter = logging.Formatter(format_str, datefmt=date_format)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, level.upper(), logging.INFO))
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器（如果指定了文件路径）
        if file_path:
            # 确保日志目录存在
            log_path = Path(file_path)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(file_path, encoding='utf-8')
            file_handler.setLevel(getattr(logging, level.upper(), logging.INFO))
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
        
        cls._initialized = True
    
    @classmethod
    def get_logger(cls, name: str) -> logging.Logger:
        """
        获取指定名称的日志器
        
        Args:
            name: 日志器名称，通常使用模块名
            
        Returns:
            logging.Logger: 配置好的日志器实例
        """
        # 确保日志系统已初始化
        if not cls._initialized:
            cls.setup_logging()
        
        # 如果已经创建过，直接返回
        if name in cls._loggers:
            return cls._loggers[name]
        
        # 创建新的日志器
        logger = logging.getLogger(name)
        cls._loggers[name] = logger
        
        return logger
    
    @classmethod
    def set_level(cls, level: str) -> None:
        """
        动态设置日志级别
        
        Args:
            level: 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        """
        log_level = getattr(logging, level.upper(), logging.INFO)
        
        # 设置根日志器级别
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 设置所有处理器级别
        for handler in root_logger.handlers:
            handler.setLevel(log_level)
    
    @classmethod
    def add_file_handler(cls, file_path: str, level: Optional[str] = None) -> None:
        """
        添加文件处理器
        
        Args:
            file_path: 日志文件路径
            level: 日志级别，默认使用配置中的级别
        """
        if not cls._initialized:
            cls.setup_logging()
        
        log_level = level or config.log_level
        
        # 确保日志目录存在
        log_path = Path(file_path)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 创建文件处理器
        file_handler = logging.FileHandler(file_path, encoding='utf-8')
        file_handler.setLevel(getattr(logging, log_level.upper(), logging.INFO))
        
        # 使用配置中的格式
        formatter = logging.Formatter(config.log_format, datefmt=config.log_date_format)
        file_handler.setFormatter(formatter)
        
        # 添加到根日志器
        root_logger = logging.getLogger()
        root_logger.addHandler(file_handler)


class LoggerMixin:
    """日志混入类，为其他类提供日志功能"""
    
    @property
    def logger(self) -> logging.Logger:
        """获取当前类的日志器"""
        if not hasattr(self, '_logger'):
            class_name = self.__class__.__name__
            module_name = self.__class__.__module__
            logger_name = f"{module_name}.{class_name}"
            self._logger = Logger.get_logger(logger_name)
        return self._logger


# 便捷函数
def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志器的便捷函数
    
    Args:
        name: 日志器名称，如果为None则使用调用者的模块名
        
    Returns:
        logging.Logger: 配置好的日志器实例
    """
    if name is None:
        # 获取调用者的模块名
        import inspect
        frame = inspect.currentframe().f_back
        name = frame.f_globals.get('__name__', 'unknown')
    
    return Logger.get_logger(name)


def setup_logging(**kwargs) -> None:
    """设置日志系统的便捷函数"""
    Logger.setup_logging(**kwargs)


def set_log_level(level: str) -> None:
    """设置日志级别的便捷函数"""
    Logger.set_level(level)


def add_file_handler(file_path: str, level: Optional[str] = None) -> None:
    """添加文件处理器的便捷函数"""
    Logger.add_file_handler(file_path, level)


# 模块级别的日志器
logger = get_logger(__name__)

# 自动初始化日志系统
Logger.setup_logging()
