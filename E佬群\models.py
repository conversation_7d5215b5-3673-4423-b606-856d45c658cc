#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据模型定义
使用dataclass定义标准数据结构，统一输入输出格式
"""

from dataclasses import dataclass, field, asdict
from typing import List, Optional, Dict, Any, Union
from enum import Enum
import json
from pathlib import Path

class PlatformType(Enum):
    """网盘平台类型枚举"""
    QUARK = "夸克"
    XUNLEI = "迅雷"
    BAIDU = "百度"
    ALIYUN = "阿里云"
    TIANYI = "天翼"
    YIDONG = "移动"
    PIKPAK = "PikPak"
    PAN123 = "123盘"
    PAN115 = "115"
    MAGNET = "磁力"
    ED2K = "ed2k"
    OTHER = "其他"

    @classmethod
    def from_url(cls, url: str) -> 'PlatformType':
        """根据URL识别平台类型"""
        url_lower = url.lower()
        
        if 'pan.quark.cn' in url_lower:
            return cls.QUARK
        elif 'pan.xunlei.com' in url_lower:
            return cls.XUNLEI
        elif 'pan.baidu.com' in url_lower:
            return cls.BAIDU
        elif 'alipan.com' in url_lower or 'aliyundrive.com' in url_lower:
            return cls.ALIYUN
        elif 'cloud.189.cn' in url_lower:
            return cls.TIANYI
        elif 'caiyun.139.com' in url_lower:
            return cls.YIDONG
        elif 'mypikpak.com' in url_lower:
            return cls.PIKPAK
        elif '123pan.com' in url_lower:
            return cls.PAN123
        elif '115.com' in url_lower:
            return cls.PAN115
        elif url_lower.startswith('magnet:'):
            return cls.MAGNET
        elif url_lower.startswith('ed2k:'):
            return cls.ED2K
        else:
            return cls.OTHER


@dataclass
class ResourceLink:
    """资源链接数据模型"""
    url: str
    platform: PlatformType = field(default=PlatformType.OTHER)
    password: Optional[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.platform == PlatformType.OTHER:
            self.platform = PlatformType.from_url(self.url)
        
        # 尝试从URL中提取密码
        if not self.password and 'pwd=' in self.url:
            try:
                pwd_part = self.url.split('pwd=')[1]
                self.password = pwd_part.split('#')[0].split('&')[0]
            except (IndexError, AttributeError):
                pass
    
    @property
    def platform_name(self) -> str:
        """获取平台名称"""
        return self.platform.value
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'url': self.url,
            'platform': self.platform.value,
            'password': self.password
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ResourceLink':
        """从字典创建实例"""
        platform = PlatformType.OTHER
        if 'platform' in data:
            # 尝试匹配平台类型
            for pt in PlatformType:
                if pt.value == data['platform']:
                    platform = pt
                    break
        
        return cls(
            url=data['url'],
            platform=platform,
            password=data.get('password')
        )


@dataclass
class Resource:
    """资源数据模型"""
    title: str
    links: List[ResourceLink] = field(default_factory=list)
    description: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    quality: Optional[str] = None  # 4K, 高清等
    status: Optional[str] = None   # 更新至第X集等
    year: Optional[int] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 确保links是ResourceLink对象列表
        if self.links and isinstance(self.links[0], (str, dict)):
            new_links = []
            for link in self.links:
                if isinstance(link, str):
                    new_links.append(ResourceLink(url=link))
                elif isinstance(link, dict):
                    new_links.append(ResourceLink.from_dict(link))
                else:
                    new_links.append(link)
            self.links = new_links
        
        # 从标题中提取信息
        self._extract_info_from_title()
    
    def _extract_info_from_title(self):
        """从标题中提取质量、状态等信息"""
        title_lower = self.title.lower()
        
        # 提取质量信息
        if '4k' in title_lower:
            self.quality = '4K'
        elif '高清' in self.title:
            self.quality = '高清'
        elif '蓝光' in self.title:
            self.quality = '蓝光'
        
        # 提取更新状态
        import re
        update_match = re.search(r'更新至\s*(\d+)', self.title)
        if update_match:
            self.status = f"更新至第{update_match.group(1)}集"
        
        # 提取年份
        year_match = re.search(r'\[?(\d{4})\]?', self.title)
        if year_match:
            self.year = int(year_match.group(1))
    
    def add_link(self, url: str, platform: Optional[PlatformType] = None, password: Optional[str] = None):
        """添加链接"""
        link = ResourceLink(url=url, platform=platform or PlatformType.OTHER, password=password)
        self.links.append(link)
    
    def get_links_by_platform(self, platform: PlatformType) -> List[ResourceLink]:
        """根据平台获取链接"""
        return [link for link in self.links if link.platform == platform]
    
    @property
    def platforms(self) -> List[PlatformType]:
        """获取所有平台类型"""
        return list(set(link.platform for link in self.links))
    
    @property
    def platform_names(self) -> List[str]:
        """获取所有平台名称"""
        return [platform.value for platform in self.platforms]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'title': self.title,
            'links': [link.to_dict() for link in self.links],
            'description': self.description,
            'tags': self.tags,
            'quality': self.quality,
            'status': self.status,
            'year': self.year
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Resource':
        """从字典创建实例"""
        # 处理旧格式兼容性
        if 'links' in data and data['links'] and isinstance(data['links'][0], str):
            # 旧格式：links是字符串列表
            links = [ResourceLink(url=url) for url in data['links']]
        else:
            # 新格式：links是字典列表
            links = [ResourceLink.from_dict(link) if isinstance(link, dict) else link 
                    for link in data.get('links', [])]
        
        return cls(
            title=data['title'],
            links=links,
            description=data.get('description'),
            tags=data.get('tags', []),
            quality=data.get('quality'),
            status=data.get('status'),
            year=data.get('year')
        )
    
    def to_legacy_dict(self) -> Dict[str, Any]:
        """转换为旧格式字典（向后兼容）"""
        return {
            'title': self.title,
            'links': [link.url for link in self.links]
        }


@dataclass
class ExtractionResult:
    """提取结果数据模型"""
    resources: List[Resource] = field(default_factory=list)
    total_count: int = 0
    filtered_count: int = 0
    source_file: Optional[str] = None
    filter_keywords: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.total_count:
            self.total_count = len(self.resources)
    
    def add_resource(self, resource: Resource):
        """添加资源"""
        self.resources.append(resource)
        self.total_count = len(self.resources)
    
    def filter_by_keywords(self, keywords: List[str]) -> 'ExtractionResult':
        """根据关键词过滤"""
        if not keywords:
            return self
        
        filtered_resources = []
        for resource in self.resources:
            # 检查标题是否包含过滤关键词
            should_filter = any(keyword in resource.title for keyword in keywords)
            if not should_filter:
                filtered_resources.append(resource)
        
        return ExtractionResult(
            resources=filtered_resources,
            total_count=len(self.resources),
            filtered_count=len(self.resources) - len(filtered_resources),
            source_file=self.source_file,
            filter_keywords=keywords
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'resources': [resource.to_dict() for resource in self.resources],
            'total_count': self.total_count,
            'filtered_count': self.filtered_count,
            'source_file': self.source_file,
            'filter_keywords': self.filter_keywords
        }
    
    def to_legacy_format(self) -> List[Dict[str, Any]]:
        """转换为旧格式（向后兼容）"""
        return [resource.to_legacy_dict() for resource in self.resources]
    
    def save_to_file(self, file_path: Union[str, Path], legacy_format: bool = False):
        """保存到文件"""
        file_path = Path(file_path)
        
        if legacy_format:
            data = self.to_legacy_format()
        else:
            data = self.to_dict()
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_from_file(cls, file_path: Union[str, Path]) -> 'ExtractionResult':
        """从文件加载"""
        file_path = Path(file_path)
        
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # 检测格式
        if isinstance(data, list):
            # 旧格式：直接是资源列表
            resources = [Resource.from_dict(item) for item in data]
            return cls(resources=resources)
        else:
            # 新格式：包含元数据的字典
            resources = [Resource.from_dict(item) for item in data.get('resources', [])]
            return cls(
                resources=resources,
                total_count=data.get('total_count', len(resources)),
                filtered_count=data.get('filtered_count', 0),
                source_file=data.get('source_file'),
                filter_keywords=data.get('filter_keywords', [])
            )


@dataclass
class TelegramConfig:
    """Telegram配置数据模型"""
    api_id: str
    api_hash: str
    string_session: str
    target_channel: str
    send_delay: float = 3.0
    
    def validate(self) -> bool:
        """验证配置"""
        return all([self.api_id, self.api_hash, self.string_session, self.target_channel])


@dataclass
class PipelineConfig:
    """管道配置数据模型"""
    input_file: str = "debug_content.txt"
    output_file: str = "filtered_resources.txt"
    resource_file: str = "filtered_resources.txt.json"
    kdocs_url: str = "https://www.kdocs.cn/l/caRody46qOEl"
    filter_keywords: List[str] = field(default_factory=lambda: ["短剧", "软件"])
    export_json: bool = True
    telegram: Optional[TelegramConfig] = None
