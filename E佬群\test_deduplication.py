#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
去重功能测试脚本
演示去重功能的工作原理
"""

from deduplication import DeduplicationManager
from logger import get_logger, setup_logging

# 设置日志
setup_logging(log_level='INFO')
logger = get_logger(__name__)

def test_deduplication():
    """测试去重功能"""
    logger.info("🧪 开始测试去重功能")
    
    # 创建去重管理器
    dedup_manager = DeduplicationManager("test_cache.json")
    
    # 测试资源
    test_resources = [
        {
            'title': '诛仙 第三季 4K 更新至 09',
            'links': ['https://pan.quark.cn/s/01d716e18958', 'https://pan.xunlei.com/s/VOT1XFkdmvBwBh68lIdiXCvmA1?pwd=ckm3#']
        },
        {
            'title': '新资源测试',
            'links': ['https://pan.quark.cn/s/newresource123']
        },
        {
            'title': '神印王座 年番 4K 更新至 169',
            'links': ['https://pan.quark.cn/s/anotherlink456']
        }
    ]
    
    logger.info(f"📊 测试 {len(test_resources)} 个资源")
    
    # 检查重复
    for i, resource in enumerate(test_resources, 1):
        title = resource['title']
        links = resource['links']
        
        is_duplicate = dedup_manager.is_duplicate(title, links)
        
        if is_duplicate:
            logger.info(f"  {i}. ❌ 重复: {title[:50]}...")
        else:
            logger.info(f"  {i}. ✅ 新资源: {title[:50]}...")
            # 模拟发送成功，添加到缓存
            dedup_manager.add_sent_resource(title, links, 1000 + i)
    
    # 保存缓存
    dedup_manager.save_cache()
    
    # 显示统计
    logger.info(f"📈 缓存统计: {dedup_manager.get_duplicate_count()} 个已发送资源")
    
    logger.info("✅ 去重功能测试完成")

def show_cache_info():
    """显示缓存信息"""
    logger.info("📋 当前缓存信息:")
    
    dedup_manager = DeduplicationManager()
    
    if not dedup_manager.sent_resources:
        logger.info("  缓存为空")
        return
    
    logger.info(f"  总计: {len(dedup_manager.sent_resources)} 个已发送资源")
    
    # 显示前5个资源
    for i, (hash_key, resource) in enumerate(list(dedup_manager.sent_resources.items())[:5], 1):
        logger.info(f"  {i}. {resource.title[:50]}... (发送时间: {resource.sent_time.strftime('%H:%M:%S')})")
    
    if len(dedup_manager.sent_resources) > 5:
        logger.info(f"  ... 还有 {len(dedup_manager.sent_resources) - 5} 个资源")

if __name__ == "__main__":
    # 显示当前缓存信息
    show_cache_info()
    print()
    
    # 测试去重功能
    test_deduplication()
