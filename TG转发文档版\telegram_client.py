import asyncio
import logging
import re
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional, Set
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.errors import FloodWaitError, ChannelPrivateError
from config import Config
from utils import MessageFormatter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TelegramForwarder:
    def __init__(self):
        self.client: Optional[TelegramClient] = None
        
    async def __aenter__(self):
        # 使用 .env 文件中的 STRING_SESSION
        if Config.TG_STRING_SESSION:
            # 使用字符串会话
            session = StringSession(Config.TG_STRING_SESSION)
        else:
            # 如果没有字符串会话，使用空会话（需要登录）
            session = StringSession()

        self.client = TelegramClient(
            session,
            Config.TG_API_ID,
            Config.TG_API_HASH
        )

        await self.client.start()
        logger.info("Telegram 客户端已连接")
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.client:
            await self.client.disconnect()
            logger.info("Telegram 客户端已断开")
    
    async def send_items_to_channel(self, items: List[Dict]) -> bool:
        """发送内容到频道"""
        try:
            # 获取频道实体
            channel = await self.client.get_entity(Config.TARGET_CHANNEL)
            logger.info(f"目标频道: {channel.title}")

            # 获取当天已发送的消息内容用于去重
            existing_content = await self.get_today_messages(channel)

            # 过滤重复内容
            filtered_items = []
            duplicate_count = 0

            for item in items:
                title = item.get('text', '')
                if self._is_duplicate_content(title, existing_content):
                    logger.info(f"跳过重复内容: {title[:50]}...")
                    duplicate_count += 1
                else:
                    filtered_items.append(item)

            if duplicate_count > 0:
                logger.info(f"过滤掉 {duplicate_count} 条重复内容")

            if not filtered_items:
                logger.info("所有内容都是重复的，无需发送")
                return True

            # 按分类分组
            grouped_items = self._group_by_category(filtered_items)

            success_count = 0
            total_count = len(filtered_items)
            
            for category, category_items in grouped_items.items():
                logger.info(f"发送分类: {category} ({len(category_items)} 条)")
                
                # 发送分类标题
                if len(category_items) > 1:
                    await self._send_category_header(channel, category)
                
                # 发送内容
                for item in category_items:
                    try:
                        await self._send_single_item(channel, item)
                        success_count += 1
                        
                        # 避免频率限制
                        await asyncio.sleep(Config.DELAY_BETWEEN_REQUESTS)
                        
                    except FloodWaitError as e:
                        logger.warning(f"遇到频率限制，等待 {e.seconds} 秒")
                        await asyncio.sleep(e.seconds)
                        # 重试
                        await self._send_single_item(channel, item)
                        success_count += 1
                        
                    except Exception as e:
                        logger.error(f"发送失败: {e}")
                        continue
            
            logger.info(f"发送完成: {success_count}/{total_count}")
            return success_count > 0
            
        except ChannelPrivateError:
            logger.error("无法访问频道，请检查权限")
            return False
        except Exception as e:
            logger.error(f"发送到频道失败: {e}")
            return False
    
    def _group_by_category(self, items: List[Dict]) -> Dict[str, List[Dict]]:
        """按分类分组"""
        groups = {}
        
        for item in items:
            category = item.get('category', '未分类')
            if category not in groups:
                groups[category] = []
            groups[category].append(item)
        
        return groups
    
    async def _send_category_header(self, channel, category: str):
        """发送分类标题"""
        header_text = f"📂 **{category}**\n" + "─" * 30
        
        try:
            await self.client.send_message(
                channel,
                header_text,
                parse_mode='markdown'
            )
        except Exception as e:
            logger.error(f"发送分类标题失败: {e}")
    
    async def _send_single_item(self, channel, item: Dict):
        """发送单个内容项"""
        # 构建消息文本
        message_text = self._format_message(item)
        
        try:
            await self.client.send_message(
                channel,
                message_text,
                parse_mode='markdown'
            )
            
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            # 尝试不使用 markdown 格式
            try:
                await self.client.send_message(
                    channel,
                    item['text']
                )
            except Exception as e2:
                logger.error(f"纯文本发送也失败: {e2}")
                raise
    
    def _format_message(self, item: Dict) -> str:
        """格式化消息"""
        content_type = item.get('type', 'general')

        # 根据内容类型使用不同的格式化器
        if content_type == 'movie':
            return MessageFormatter.format_movie_message(item)
        elif content_type == 'tv_series':
            return MessageFormatter.format_tv_series_message(item)
        else:
            return MessageFormatter.format_general_message(item)
    
    async def test_connection(self) -> bool:
        """测试连接"""
        try:
            me = await self.client.get_me()
            logger.info(f"已连接账号: {me.first_name}")

            # 测试频道访问
            channel = await self.client.get_entity(Config.TARGET_CHANNEL)
            logger.info(f"可访问频道: {channel.title}")

            return True
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return False

    async def get_today_messages(self, channel) -> List[Dict]:
        """获取当天已发送的消息内容信息"""
        try:
            # 获取今天的开始时间（UTC+8）
            china_tz = timezone(timedelta(hours=8))
            today_start = datetime.now(china_tz).replace(hour=0, minute=0, second=0, microsecond=0)
            today_start_utc = today_start.astimezone(timezone.utc)

            content_info = []
            async for message in self.client.iter_messages(channel, offset_date=today_start_utc):
                if message.text:
                    title = self._extract_title_from_message(message.text)
                    if title:
                        info = self._parse_content_info(title)
                        content_info.append(info)

            logger.info(f"获取到当天已发送的 {len(content_info)} 条消息内容")
            return content_info

        except Exception as e:
            logger.error(f"获取当天消息失败: {e}")
            return []

    def _extract_title_from_message(self, message_text: str) -> Optional[str]:
        """从消息中提取标题"""
        if not message_text:
            return None

        # 提取加粗的标题（**标题**格式）
        bold_match = re.search(r'\*\*(.+?)\*\*', message_text)
        if bold_match:
            return bold_match.group(1).strip()

        # 如果没有加粗格式，取第一行作为标题
        first_line = message_text.split('\n')[0].strip()
        return first_line if first_line else None

    def _parse_content_info(self, title: str) -> Dict:
        """解析内容信息，提取主标题和更新信息"""
        if not title:
            return {"main_title": "", "episode_info": None, "full_title": title}

        # 提取主标题（第一个括号前的内容）
        main_title_match = re.match(r'^([^[\(（【]+)', title)
        main_title = main_title_match.group(1).strip() if main_title_match else title

        # 提取更新信息
        episode_info = self._extract_episode_info(title)

        return {
            "main_title": main_title,
            "episode_info": episode_info,
            "full_title": title
        }

    def _extract_episode_info(self, text: str) -> Optional[Dict]:
        """提取集数/季数信息"""
        if not text:
            return None

        # 匹配各种更新模式
        patterns = [
            r'更新至(\d+)集?',
            r'更新至第?(\d+)集',
            r'第(\d+)集',
            r'全(\d+)集',
            r'共(\d+)集',
            r'第(\d+)季',
            r'[Ss](\d+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, text)
            if match:
                return {
                    "type": "episode" if "集" in pattern or "更新" in pattern else "season",
                    "number": int(match.group(1)),
                    "pattern": pattern
                }

        return None

    def _normalize_title(self, title: str) -> str:
        """标准化标题用于比较"""
        if not title:
            return ""

        # 转换为小写
        normalized = title.lower()

        # 移除特殊字符和空格
        normalized = re.sub(r'[^\w\u4e00-\u9fff]', '', normalized)

        return normalized

    def _is_duplicate_content(self, new_title: str, existing_content: List[Dict]) -> bool:
        """智能检查内容是否重复"""
        if not new_title or not existing_content:
            return False

        # 解析新内容信息
        new_info = self._parse_content_info(new_title)
        new_main_title = self._normalize_title(new_info["main_title"])

        if not new_main_title:
            return False

        for existing_info in existing_content:
            existing_main_title = self._normalize_title(existing_info["main_title"])

            # 如果主标题不同，不重复
            if existing_main_title != new_main_title:
                continue

            # 主标题相同，检查更新信息
            new_episode = new_info.get("episode_info")
            existing_episode = existing_info.get("episode_info")

            # 如果都没有集数信息，认为重复
            if not new_episode and not existing_episode:
                return True

            # 如果只有一个有集数信息，不重复
            if not new_episode or not existing_episode:
                continue

            # 如果类型不同（集数vs季数），不重复
            if new_episode["type"] != existing_episode["type"]:
                continue

            # 如果集数/季数相同，认为重复
            if new_episode["number"] == existing_episode["number"]:
                return True

        return False
