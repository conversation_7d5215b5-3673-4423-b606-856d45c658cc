#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自定义异常类模块
定义业务相关的异常类型
"""

class BaseResourceError(Exception):
    """资源处理基础异常类"""
    
    def __init__(self, message: str, details: str = None, original_error: Exception = None):
        """
        初始化异常
        
        Args:
            message: 异常消息
            details: 详细信息
            original_error: 原始异常
        """
        self.message = message
        self.details = details
        self.original_error = original_error
        
        # 构建完整的错误消息
        full_message = message
        if details:
            full_message += f" - {details}"
        if original_error:
            full_message += f" (原因: {str(original_error)})"
            
        super().__init__(full_message)


class ConfigurationError(BaseResourceError):
    """配置错误异常"""
    pass


class FileProcessingError(BaseResourceError):
    """文件处理错误异常"""
    pass


class ResourceExtractionError(BaseResourceError):
    """资源提取错误异常"""
    pass


class NetworkError(BaseResourceError):
    """网络请求错误异常"""
    pass


class TelegramError(BaseResourceError):
    """Telegram相关错误异常"""
    pass


class ContentParsingError(BaseResourceError):
    """内容解析错误异常"""
    pass


class ValidationError(BaseResourceError):
    """数据验证错误异常"""
    pass


# 异常处理装饰器
import functools
from typing import Callable, Any, Type, Union, List
from logger import get_logger

def handle_exceptions(
    exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception,
    default_return: Any = None,
    log_error: bool = True,
    reraise: bool = False,
    custom_message: str = None
):
    """
    异常处理装饰器
    
    Args:
        exceptions: 要捕获的异常类型或类型列表
        default_return: 发生异常时的默认返回值
        log_error: 是否记录错误日志
        reraise: 是否重新抛出异常
        custom_message: 自定义错误消息
    """
    if not isinstance(exceptions, (list, tuple)):
        exceptions = [exceptions]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except tuple(exceptions) as e:
                if log_error:
                    logger = get_logger(func.__module__)
                    error_msg = custom_message or f"函数 {func.__name__} 执行出错"
                    logger.error(f"{error_msg}: {str(e)}", exc_info=True)
                
                if reraise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator


def handle_async_exceptions(
    exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception,
    default_return: Any = None,
    log_error: bool = True,
    reraise: bool = False,
    custom_message: str = None
):
    """
    异步函数异常处理装饰器
    
    Args:
        exceptions: 要捕获的异常类型或类型列表
        default_return: 发生异常时的默认返回值
        log_error: 是否记录错误日志
        reraise: 是否重新抛出异常
        custom_message: 自定义错误消息
    """
    if not isinstance(exceptions, (list, tuple)):
        exceptions = [exceptions]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except tuple(exceptions) as e:
                if log_error:
                    logger = get_logger(func.__module__)
                    error_msg = custom_message or f"异步函数 {func.__name__} 执行出错"
                    logger.error(f"{error_msg}: {str(e)}", exc_info=True)
                
                if reraise:
                    raise
                
                return default_return
        
        return wrapper
    return decorator


class ErrorContext:
    """错误上下文管理器"""
    
    def __init__(self, 
                 operation: str,
                 logger_name: str = None,
                 reraise: bool = True,
                 default_return: Any = None):
        """
        初始化错误上下文
        
        Args:
            operation: 操作描述
            logger_name: 日志器名称
            reraise: 是否重新抛出异常
            default_return: 默认返回值
        """
        self.operation = operation
        self.logger = get_logger(logger_name or __name__)
        self.reraise = reraise
        self.default_return = default_return
        self.exception = None
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.exception = exc_val
            self.logger.error(f"{self.operation} 失败: {str(exc_val)}", exc_info=True)
            
            if not self.reraise:
                return True  # 抑制异常
        
        return False
    
    def get_result(self, success_value: Any = None) -> Any:
        """获取操作结果"""
        if self.exception is not None:
            return self.default_return
        return success_value


# 重试机制
import time
import random
from typing import Optional

def retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    jitter: bool = True,
    exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception,
    logger_name: str = None
):
    """
    重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避倍数
        jitter: 是否添加随机抖动
        exceptions: 需要重试的异常类型
        logger_name: 日志器名称
    """
    if not isinstance(exceptions, (list, tuple)):
        exceptions = [exceptions]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            logger = get_logger(logger_name or func.__module__)
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except tuple(exceptions) as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"函数 {func.__name__} 重试 {max_attempts} 次后仍然失败: {str(e)}")
                        raise
                    
                    wait_time = delay * (backoff ** attempt)
                    if jitter:
                        wait_time *= (0.5 + random.random())
                    
                    logger.warning(f"函数 {func.__name__} 第 {attempt + 1} 次尝试失败，{wait_time:.2f}秒后重试: {str(e)}")
                    time.sleep(wait_time)
            
            return None
        
        return wrapper
    return decorator


def async_retry(
    max_attempts: int = 3,
    delay: float = 1.0,
    backoff: float = 2.0,
    jitter: bool = True,
    exceptions: Union[Type[Exception], List[Type[Exception]]] = Exception,
    logger_name: str = None
):
    """
    异步重试装饰器
    
    Args:
        max_attempts: 最大重试次数
        delay: 初始延迟时间（秒）
        backoff: 退避倍数
        jitter: 是否添加随机抖动
        exceptions: 需要重试的异常类型
        logger_name: 日志器名称
    """
    if not isinstance(exceptions, (list, tuple)):
        exceptions = [exceptions]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            import asyncio
            logger = get_logger(logger_name or func.__module__)
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except tuple(exceptions) as e:
                    if attempt == max_attempts - 1:
                        logger.error(f"异步函数 {func.__name__} 重试 {max_attempts} 次后仍然失败: {str(e)}")
                        raise
                    
                    wait_time = delay * (backoff ** attempt)
                    if jitter:
                        wait_time *= (0.5 + random.random())
                    
                    logger.warning(f"异步函数 {func.__name__} 第 {attempt + 1} 次尝试失败，{wait_time:.2f}秒后重试: {str(e)}")
                    await asyncio.sleep(wait_time)
            
            return None
        
        return wrapper
    return decorator
