import asyncio
import logging
import sys
from datetime import datetime
from pathlib import Path
from scraper import KDocsScraper
from telegram_client import TelegramForwarder
from config import Config

# 获取脚本所在目录
SCRIPT_DIR = Path(__file__).parent

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(SCRIPT_DIR / 'app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class DocumentForwarder:
    def __init__(self):
        self.scraper = None
        self.telegram = None
    
    async def run(self):
        """主运行流程"""
        try:
            # 验证配置
            Config.validate()
            logger.info("配置验证通过")
            
            # 初始化组件
            async with KDocsScraper() as scraper, TelegramForwarder() as telegram:
                self.scraper = scraper
                self.telegram = telegram
                
                # 测试 Telegram 连接
                if not await telegram.test_connection():
                    logger.error("Telegram 连接测试失败")
                    return False
                
                # 爬取文档内容
                logger.info("开始爬取文档...")
                items = await scraper.scrape_document(Config.KDOCS_URL)
                
                if not items:
                    logger.warning("未获取到任何内容")
                    return False
                
                logger.info(f"获取到 {len(items)} 条内容")
                
                # 发送到 Telegram 频道
                logger.info("开始发送到 Telegram 频道...")
                success = await telegram.send_items_to_channel(items)
                
                if success:
                    logger.info("✅ 任务完成！")
                    return True
                else:
                    logger.error("❌ 发送失败")
                    return False
                    
        except ValueError as e:
            logger.error(f"配置错误: {e}")
            return False
        except Exception as e:
            logger.error(f"运行出错: {e}")
            return False
    
    async def run_with_retry(self, max_retries: int = None):
        """带重试的运行"""
        if max_retries is None:
            max_retries = Config.MAX_RETRIES
            
        for attempt in range(max_retries):
            try:
                logger.info(f"第 {attempt + 1} 次尝试...")
                success = await self.run()
                
                if success:
                    return True
                    
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 30  # 递增等待时间
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                    
            except KeyboardInterrupt:
                logger.info("用户中断")
                return False
            except Exception as e:
                logger.error(f"第 {attempt + 1} 次尝试失败: {e}")
                
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 30
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
        
        logger.error(f"所有 {max_retries} 次尝试都失败了")
        return False

async def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("TG 转发文档版 启动")
    logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 50)
    
    forwarder = DocumentForwarder()
    
    try:
        # 运行主程序
        success = await forwarder.run_with_retry()
        
        if success:
            logger.info("🎉 程序执行成功！")
            sys.exit(0)
        else:
            logger.error("💥 程序执行失败！")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("👋 程序被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"💥 程序异常退出: {e}")
        sys.exit(1)

if __name__ == "__main__":
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        sys.exit(1)
    
    # 运行程序
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 程序被中断")
    except Exception as e:
        print(f"💥 启动失败: {e}")
        sys.exit(1)
