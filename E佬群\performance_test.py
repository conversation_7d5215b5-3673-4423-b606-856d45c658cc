#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
性能测试脚本
测试各个模块的性能表现和优化效果
"""

import time
import asyncio
from pathlib import Path
from logger import get_logger, setup_logging
from performance import performance_monitor, memory_cache, file_cache
from extract_resources import extract_resources
from test_kdocs_fetch import KDocsContentTester
from config import config

# 设置日志
setup_logging(log_level='INFO')
logger = get_logger(__name__)

def test_extract_performance():
    """测试资源提取性能"""
    logger.info("开始测试资源提取性能...")
    
    input_file = config.input_file
    if not Path(input_file).exists():
        logger.error(f"测试文件不存在: {input_file}")
        return
    
    # 多次运行测试
    runs = 5
    times = []
    
    for i in range(runs):
        start_time = time.time()
        result = extract_resources(input_file, config.filter_keywords)
        end_time = time.time()
        
        duration = end_time - start_time
        times.append(duration)
        
        logger.info(f"第 {i+1} 次运行: {duration:.3f}秒, 提取 {len(result.resources)} 个资源")
    
    # 计算统计信息
    avg_time = sum(times) / len(times)
    min_time = min(times)
    max_time = max(times)
    
    logger.info(f"资源提取性能统计:")
    logger.info(f"  平均时间: {avg_time:.3f}秒")
    logger.info(f"  最短时间: {min_time:.3f}秒")
    logger.info(f"  最长时间: {max_time:.3f}秒")
    
    return {
        'average': avg_time,
        'min': min_time,
        'max': max_time,
        'runs': runs
    }

def test_cache_performance():
    """测试缓存性能"""
    logger.info("开始测试缓存性能...")
    
    # 测试内存缓存
    logger.info("测试内存缓存...")
    
    # 写入测试
    start_time = time.time()
    for i in range(1000):
        memory_cache.set(f"test_key_{i}", f"test_value_{i}")
    write_time = time.time() - start_time
    
    # 读取测试
    start_time = time.time()
    hit_count = 0
    for i in range(1000):
        value = memory_cache.get(f"test_key_{i}")
        if value is not None:
            hit_count += 1
    read_time = time.time() - start_time
    
    logger.info(f"内存缓存性能:")
    logger.info(f"  写入1000项: {write_time:.3f}秒")
    logger.info(f"  读取1000项: {read_time:.3f}秒")
    logger.info(f"  命中率: {hit_count/1000*100:.1f}%")
    logger.info(f"  缓存大小: {memory_cache.size()}")
    
    # 清理
    memory_cache.clear()
    
    # 测试文件缓存
    logger.info("测试文件缓存...")
    
    start_time = time.time()
    for i in range(100):  # 文件缓存测试较少项目
        file_cache.set(f"file_test_key_{i}", f"file_test_value_{i}")
    file_write_time = time.time() - start_time
    
    start_time = time.time()
    file_hit_count = 0
    for i in range(100):
        value = file_cache.get(f"file_test_key_{i}")
        if value is not None:
            file_hit_count += 1
    file_read_time = time.time() - start_time
    
    logger.info(f"文件缓存性能:")
    logger.info(f"  写入100项: {file_write_time:.3f}秒")
    logger.info(f"  读取100项: {file_read_time:.3f}秒")
    logger.info(f"  命中率: {file_hit_count/100*100:.1f}%")
    
    # 清理
    file_cache.clear()
    
    return {
        'memory_cache': {
            'write_time': write_time,
            'read_time': read_time,
            'hit_rate': hit_count/1000
        },
        'file_cache': {
            'write_time': file_write_time,
            'read_time': file_read_time,
            'hit_rate': file_hit_count/100
        }
    }

def test_kdocs_fetch_with_cache():
    """测试金山文档获取的缓存效果"""
    logger.info("开始测试金山文档获取缓存效果...")
    
    tester = KDocsContentTester()
    test_url = config.kdocs_url
    
    # 第一次获取（无缓存）
    logger.info("第一次获取（无缓存）...")
    start_time = time.time()
    content1 = tester.fetch_kdocs_content(test_url)
    first_time = time.time() - start_time
    
    # 第二次获取（有缓存）
    logger.info("第二次获取（有缓存）...")
    start_time = time.time()
    content2 = tester.fetch_kdocs_content(test_url)
    second_time = time.time() - start_time
    
    # 验证内容一致性
    content_match = content1 == content2 if content1 and content2 else False
    
    logger.info(f"金山文档获取缓存测试:")
    logger.info(f"  第一次获取: {first_time:.3f}秒")
    logger.info(f"  第二次获取: {second_time:.3f}秒")
    logger.info(f"  性能提升: {(first_time - second_time) / first_time * 100:.1f}%")
    logger.info(f"  内容一致: {content_match}")
    
    return {
        'first_time': first_time,
        'second_time': second_time,
        'improvement': (first_time - second_time) / first_time if first_time > 0 else 0,
        'content_match': content_match
    }

def show_performance_stats():
    """显示性能统计信息"""
    logger.info("性能监控统计:")
    
    stats = performance_monitor.get_all_stats()
    if not stats:
        logger.info("  暂无性能数据")
        return
    
    for operation, stat in stats.items():
        if stat:
            logger.info(f"  {operation}:")
            logger.info(f"    调用次数: {stat['count']}")
            logger.info(f"    总时间: {stat['total']:.3f}秒")
            logger.info(f"    平均时间: {stat['average']:.3f}秒")
            logger.info(f"    最短时间: {stat['min']:.3f}秒")
            logger.info(f"    最长时间: {stat['max']:.3f}秒")

def main():
    """主测试函数"""
    logger.info("🚀 开始性能测试")
    logger.info("=" * 60)
    
    # 测试资源提取性能
    extract_stats = test_extract_performance()
    logger.info("=" * 60)
    
    # 测试缓存性能
    cache_stats = test_cache_performance()
    logger.info("=" * 60)
    
    # 测试金山文档获取缓存效果
    kdocs_stats = test_kdocs_fetch_with_cache()
    logger.info("=" * 60)
    
    # 显示性能监控统计
    show_performance_stats()
    logger.info("=" * 60)
    
    # 总结
    logger.info("📊 性能测试总结:")
    logger.info(f"✅ 资源提取平均耗时: {extract_stats['average']:.3f}秒")
    logger.info(f"✅ 内存缓存命中率: {cache_stats['memory_cache']['hit_rate']*100:.1f}%")
    logger.info(f"✅ 文件缓存命中率: {cache_stats['file_cache']['hit_rate']*100:.1f}%")
    logger.info(f"✅ 网络请求缓存提升: {kdocs_stats['improvement']*100:.1f}%")
    
    logger.info("🎉 性能测试完成！")

if __name__ == "__main__":
    main()
