#!/usr/bin/env python3
"""
安装脚本 - 自动安装依赖和配置环境
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, description=""):
    """运行命令并处理错误"""
    print(f"🔄 {description}")
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} 完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} 失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要 Python 3.8 或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    
    print(f"✅ Python 版本检查通过: {sys.version}")
    return True

def install_dependencies():
    """安装 Python 依赖"""
    print("\n📦 安装 Python 依赖...")
    
    # 升级 pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级 pip"):
        return False
    
    # 安装依赖
    if not run_command(f"{sys.executable} -m pip install -r requirements.txt", "安装项目依赖"):
        return False
    
    return True

def install_playwright():
    """安装 Playwright 浏览器"""
    print("\n🌐 安装 Playwright 浏览器...")
    
    if not run_command("playwright install chromium", "安装 Chromium 浏览器"):
        return False
    
    return True

def setup_config():
    """设置配置文件"""
    print("\n⚙️ 设置配置文件...")

    # 获取脚本所在目录
    script_dir = Path(__file__).parent
    env_example = script_dir / ".env.example"
    env_file = script_dir / ".env"

    if not env_example.exists():
        print("❌ .env.example 文件不存在，创建默认配置...")
        # 创建默认的 .env.example 文件
        default_config = """# Telegram API 配置
TG_API_ID=your_api_id
TG_API_HASH=your_api_hash
TG_STRING_SESSION=your_string_session

# 目标频道配置
TARGET_CHANNEL=@your_channel_username
# 或者使用频道ID: TARGET_CHANNEL=-1001234567890

# 金山文档链接
KDOCS_URL=https://www.kdocs.cn/l/caRody46qOEl

# 爬取配置
HEADLESS=true
DELAY_BETWEEN_REQUESTS=2
MAX_RETRIES=3

# 分类配置
ENABLE_CLASSIFICATION=true
MIN_CONTENT_LENGTH=10"""

        try:
            with open(env_example, 'w', encoding='utf-8') as f:
                f.write(default_config)
            print("✅ 已创建 .env.example 文件")
        except Exception as e:
            print(f"❌ 创建 .env.example 失败: {e}")
            return False

    if env_file.exists():
        print("⚠️ .env 文件已存在，跳过创建")
        return True

    try:
        shutil.copy(env_example, env_file)
        print("✅ 已创建 .env 配置文件")
        print("📝 请编辑 .env 文件填入你的配置信息")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def check_requirements():
    """检查系统要求"""
    print("🔍 检查系统要求...")
    
    # 检查必要的命令
    commands = ['git', 'pip']
    for cmd in commands:
        if not shutil.which(cmd):
            print(f"❌ 未找到命令: {cmd}")
            return False
        else:
            print(f"✅ 找到命令: {cmd}")
    
    return True

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*50)
    print("🎉 安装完成！")
    print("="*50)
    print("\n📋 后续步骤:")
    print("1. 编辑 .env 文件，填入你的 Telegram API 配置")
    print("2. 设置目标频道信息")
    print("3. 运行程序: python main.py")
    print("\n📖 配置说明:")
    print("- TG_API_ID 和 TG_API_HASH: 从 https://my.telegram.org 获取")
    print("- TG_STRING_SESSION: 通过其他工具获取的会话字符串")
    print("- TARGET_CHANNEL: 目标频道用户名或ID")
    print("\n🔗 更多帮助请查看 README.md")

def main():
    """主安装流程"""
    print("🚀 TG 转发文档版 - 安装程序")
    print("="*50)
    
    # 检查 Python 版本
    if not check_python_version():
        sys.exit(1)
    
    # 检查系统要求
    if not check_requirements():
        print("❌ 系统要求检查失败，请安装缺失的工具")
        sys.exit(1)
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败")
        sys.exit(1)
    
    # 安装 Playwright
    if not install_playwright():
        print("❌ Playwright 安装失败")
        sys.exit(1)
    
    # 设置配置
    if not setup_config():
        print("❌ 配置设置失败")
        sys.exit(1)
    
    # 显示后续步骤
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 安装被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n💥 安装过程中出现错误: {e}")
        sys.exit(1)
