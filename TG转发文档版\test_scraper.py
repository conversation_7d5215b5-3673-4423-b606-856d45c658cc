#!/usr/bin/env python3
"""
简单的爬取测试脚本
"""

import asyncio
import json
from scraper import KDocsScraper
from config import Config

async def test_scraper():
    """测试爬取功能"""
    print("🧪 开始测试爬取功能...")
    print(f"📄 目标URL: {Config.KDOCS_URL}")
    print("-" * 50)
    
    try:
        async with KDocsScraper() as scraper:
            items = await scraper.scrape_document(Config.KDOCS_URL)
            
            print(f"✅ 爬取完成，共获取 {len(items)} 条内容")
            
            # 统计信息
            categories = {}
            types = {}
            links_count = 0
            
            for item in items:
                # 分类统计
                cat = item.get('category', '未知')
                categories[cat] = categories.get(cat, 0) + 1
                
                # 类型统计
                typ = item.get('type', '未知')
                types[typ] = types.get(typ, 0) + 1
                
                # 链接统计
                if item.get('links'):
                    links_count += len(item['links'])
            
            print(f"\n📊 统计信息:")
            print(f"分类数量: {len(categories)}")
            print(f"内容类型: {len(types)}")
            print(f"总链接数: {links_count}")
            
            print(f"\n📂 分类分布:")
            for cat, count in categories.items():
                print(f"  {cat}: {count} 条")
            
            print(f"\n🏷️ 类型分布:")
            for typ, count in types.items():
                print(f"  {typ}: {count} 条")
            
            # 显示前5条内容
            print(f"\n📝 内容预览 (前5条):")
            for i, item in enumerate(items[:5], 1):
                print(f"\n[{i}] {item.get('category', '未知')}")
                print(f"    类型: {item.get('type', '未知')}")
                print(f"    内容: {item.get('text', '')[:80]}...")
                links = item.get('links', [])
                if links:
                    print(f"    链接: {len(links)} 个")
                    for link in links[:2]:  # 只显示前2个链接
                        print(f"      - {link.get('name', '未知')}: {link.get('url', '')[:50]}...")
                else:
                    print(f"    链接: 无")
            
            return items
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_patterns():
    """测试模式匹配"""
    print("\n🔍 测试模式匹配...")
    
    from scraper import KDocsScraper
    scraper = KDocsScraper()
    
    # 测试分类标题
    test_titles = [
        "2025.05.22 周四更新的",
        "2024.12.31 周二更新",
        "热门资源",
        "【电影专区】",
        "第一期",
        "普通文本内容"
    ]
    
    print("\n📋 分类标题测试:")
    for title in test_titles:
        result = scraper._is_category_title(title)
        print(f"  '{title}' -> {'✅' if result else '❌'}")
    
    # 测试噪音内容
    test_noise = [
        "点击这里查看",
        "更多精彩内容",
        "加载中...",
        "正常的内容文本",
        "",
        "a"  # 太短
    ]
    
    print("\n🗑️ 噪音内容测试:")
    for noise in test_noise:
        result = scraper._is_noise_content(noise)
        print(f"  '{noise}' -> {'🗑️' if result else '✅'}")
    
    # 测试内容分类
    test_contents = [
        "《阿凡达2》[2024]4K高码版",
        "《庆余年》第二季 全36集",
        "《霸道总裁爱上我》(30集)短剧",
        "Photoshop 2024 破解版",
        "《鬼灭之刃》动漫全集",
        "《快乐大本营》综艺节目",
        "普通文档内容"
    ]
    
    print("\n🏷️ 内容分类测试:")
    for content in test_contents:
        result = scraper._classify_content(content)
        print(f"  '{content}' -> {result}")

if __name__ == "__main__":
    try:
        # 运行爬取测试
        items = asyncio.run(test_scraper())
        
        # 运行模式测试
        test_patterns()
        
        print(f"\n🎉 测试完成！")
        
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试出错: {e}")
        import traceback
        traceback.print_exc()
