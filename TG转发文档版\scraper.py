import logging
from typing import List, Dict, Optional
from playwright.async_api import async_playwright, <PERSON>, Browser
from bs4 import BeautifulSoup
import re
from config import Config

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class KDocsScraper:
    def __init__(self):
        self.browser: Optional[Browser] = None
        self.page: Optional[Page] = None
        
    async def __aenter__(self):
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(
            headless=Config.HEADLESS,
            args=['--no-sandbox', '--disable-dev-shm-usage']
        )
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        await self.page.set_extra_http_headers({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    async def scrape_document(self, url: str) -> List[Dict]:
        """爬取金山文档内容"""
        try:
            logger.info(f"开始爬取文档: {url}")

            # 访问页面
            await self.page.goto(url, wait_until='networkidle', timeout=30000)

            # 等待内容加载
            await self.page.wait_for_timeout(5000)

            # 尝试等待特定元素加载
            try:
                await self.page.wait_for_selector('div, p, span', timeout=10000)
            except:
                logger.warning("未找到预期的内容元素，继续尝试...")

            # 获取页面内容
            content = await self.page.content()
            soup = BeautifulSoup(content, 'lxml')

            # 调试：输出页面结构信息
            logger.info(f"页面标题: {soup.title.string if soup.title else '无标题'}")
            all_text_elements = soup.find_all(text=True)
            visible_texts = [text.strip() for text in all_text_elements if text.strip() and len(text.strip()) > 5]
            logger.info(f"找到可见文本数量: {len(visible_texts)}")

            # 提取文档内容
            items = await self._extract_content(soup)

            logger.info(f"成功提取 {len(items)} 条内容")
            return items

        except Exception as e:
            logger.error(f"爬取失败: {e}")
            return []
    
    async def _extract_content(self, soup: BeautifulSoup) -> List[Dict]:
        """基于实际页面结构提取内容"""
        items = []

        # 专门针对金山文档的结构化提取
        items.extend(self._extract_structured_content(soup))

        # 如果结构化提取失败，使用通用方法
        if not items:
            logger.info("结构化提取无结果，使用通用方法...")
            items.extend(self._extract_generic_content(soup))

        logger.info(f"提取到 {len(items)} 条原始内容")

        # 后处理：提取真正的内容项
        content_items = self._extract_content_items(items)
        logger.info(f"内容提取后剩余 {len(content_items)} 条内容")

        # 增强链接提取
        self._enhance_links_for_items(content_items, soup)

        deduplicated_items = self._deduplicate_items(content_items)
        logger.info(f"去重后剩余 {len(deduplicated_items)} 条内容")

        return deduplicated_items

    def _extract_structured_content(self, soup: BeautifulSoup) -> List[Dict]:
        """基于页面结构提取内容，专注于更新部分"""
        items = []
        current_category = "未分类"

        # 查找所有标题元素（分类）
        headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        logger.info(f"找到 {len(headings)} 个标题元素")

        for heading in headings:
            heading_text = heading.get_text(strip=True)
            if self._is_category_title(heading_text):
                current_category = heading_text
                logger.info(f"发现分类标题: {heading_text}")

                # 只处理更新部分，跳过热门资源
                if "热门资源" in heading_text:
                    logger.info(f"跳过热门资源部分: {heading_text}")
                    continue

                # 查找该标题后的内容
                items.extend(self._extract_content_after_heading(heading, current_category))

        # 查找所有strong元素（内容项），但只处理更新部分
        strong_elements = soup.find_all('strong')
        logger.info(f"找到 {len(strong_elements)} 个strong元素")

        # 标记是否在更新部分
        in_update_section = False

        for strong in strong_elements:
            text = strong.get_text(strip=True)

            # 检查是否是分类标题
            if self._is_category_title(text):
                if "热门资源" in text:
                    in_update_section = False
                    logger.info(f"进入热门资源区域，跳过: {text}")
                    continue
                elif "更新" in text:
                    in_update_section = True
                    current_category = text
                    logger.info(f"进入更新区域: {text}")
                    continue
                else:
                    current_category = text
                    continue

            # 跳过噪音内容
            if self._is_noise_content(text):
                continue

            # 只处理更新部分的内容
            if not in_update_section:
                continue

            if len(text) >= Config.MIN_CONTENT_LENGTH:
                # 查找相关链接
                links = self._extract_nearby_links(strong, text)

                item = {
                    'text': text,
                    'category': current_category,
                    'type': self._classify_content(text),
                    'links': links,
                    'timestamp': None
                }
                items.append(item)
                logger.debug(f"添加更新内容: {text[:50]}...")

        return items

    def _extract_content_after_heading(self, heading, category: str) -> List[Dict]:
        """提取标题后的内容"""
        items = []

        # 查找标题后的兄弟元素
        current = heading.next_sibling
        while current:
            if hasattr(current, 'name'):
                # 如果遇到下一个标题，停止
                if current.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                    break

                # 提取文本内容
                if current.name in ['strong', 'p', 'div']:
                    text = current.get_text(strip=True)
                    if text and len(text) >= Config.MIN_CONTENT_LENGTH and not self._is_noise_content(text):
                        links = self._extract_nearby_links(current, text)

                        item = {
                            'text': text,
                            'category': category,
                            'type': self._classify_content(text),
                            'links': links,
                            'timestamp': None
                        }
                        items.append(item)

            current = current.next_sibling

        return items

    def _extract_generic_content(self, soup: BeautifulSoup) -> List[Dict]:
        """通用内容提取方法（备用）"""
        items = []
        current_category = "未分类"

        # 使用配置的选择器
        text_elements = []
        for selector in Config.SCRAPER_SELECTORS:
            try:
                elements = soup.select(selector)
                if elements:
                    text_elements.extend(elements)
                    logger.info(f"使用选择器 '{selector}' 找到 {len(elements)} 个元素")
            except Exception as e:
                logger.warning(f"选择器 '{selector}' 失败: {e}")

        processed_texts = set()

        for element in text_elements:
            if hasattr(element, 'get_text'):
                text = element.get_text(strip=True)
            else:
                text = str(element).strip()

            if not text or len(text) < Config.MIN_CONTENT_LENGTH:
                continue

            text_key = text[:100]
            if text_key in processed_texts:
                continue
            processed_texts.add(text_key)

            if self._is_noise_content(text):
                continue

            if self._is_category_title(text):
                current_category = text
                logger.info(f"发现分类标题: {text}")
                continue

            links = self._extract_nearby_links(element, text) if hasattr(element, 'parent') else []

            item = {
                'text': text,
                'category': current_category,
                'type': self._classify_content(text),
                'links': links,
                'timestamp': None
            }

            items.append(item)

        return items

    def _enhance_links_for_items(self, items: List[Dict], soup: BeautifulSoup):
        """为内容项增强链接提取，基于DOM顺序匹配"""
        # 获取所有网盘链接，保持DOM顺序
        all_links = []
        for link in soup.find_all('a', href=True):
            href = link.get('href', '')
            link_type, name = self._identify_link_type(href)
            if link_type:
                all_links.append({
                    'url': href,
                    'type': link_type,
                    'name': name,
                    'text': link.get_text(strip=True),
                    'element': link  # 保存DOM元素引用
                })

        logger.info(f"找到 {len(all_links)} 个网盘链接")

        # 调试：显示传入的内容项结构
        logger.info(f"传入的内容项数量: {len(items)}")
        for i, item in enumerate(items[:3], 1):  # 只显示前3个
            logger.info(f"内容项[{i}]: text='{item.get('text', '')[:30]}...', links={item.get('links', 'None')}")

        # 基于DOM顺序进行内容和链接的匹配
        self._match_content_links_by_dom_order(items, all_links, soup)

    def _match_content_links_by_dom_order(self, items: List[Dict], all_links: List[Dict], soup: BeautifulSoup):
        """基于DOM顺序匹配内容和链接"""
        # 找到更新部分的标题
        update_heading = None
        all_headings = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'])
        logger.info(f"找到 {len(all_headings)} 个标题元素")

        for heading in all_headings:
            heading_text = heading.get_text(strip=True)
            logger.info(f"检查标题: '{heading_text}'")
            if "更新" in heading_text:
                update_heading = heading
                logger.info(f"找到更新标题: '{heading_text}'")
                break

        if not update_heading:
            logger.warning("未找到更新部分标题，使用简单顺序分配")
            self._simple_sequential_assignment(items, all_links)
            return

        # 从更新标题开始，按DOM顺序收集内容和链接
        content_link_pairs = []
        current_content = None
        current_links = []

        # 获取更新标题的父容器，然后遍历其中的所有元素
        container = update_heading.parent
        if not container:
            logger.warning("无法找到更新标题的父容器")
            self._simple_sequential_assignment(items, all_links)
            return

        # 调试：显示容器信息
        logger.info(f"更新标题的父容器标签: {container.name}")
        logger.info(f"父容器的子元素数量: {len(list(container.children))}")

        # 尝试向上查找更大的容器
        current_container = container
        max_attempts = 3
        for attempt in range(max_attempts):
            parent_container = current_container.parent
            if parent_container:
                parent_children_count = len(list(parent_container.children))
                logger.info(f"尝试 {attempt + 1}: 上级容器 {parent_container.name}, 子元素数: {parent_children_count}")
                if parent_children_count > 10:  # 如果上级容器有更多子元素，使用它
                    current_container = parent_container
                    logger.info(f"选择上级容器: {parent_container.name}")
                    break
            else:
                break

        # 使用选定的容器
        container = current_container

        # 找到更新标题在容器中的位置
        all_elements = container.find_all(recursive=True)
        update_index = -1
        for i, element in enumerate(all_elements):
            if element == update_heading:
                update_index = i
                break

        if update_index == -1:
            logger.warning("无法确定更新标题的位置")
            self._simple_sequential_assignment(items, all_links)
            return

        logger.info(f"最终容器: {container.name}, 更新标题位置: {update_index}, 总元素数: {len(all_elements)}")

        # 从更新标题后开始处理，支持智能分组
        group_mode = False  # 是否处于分组模式
        group_content = None  # 分组的主标题

        for i in range(update_index + 1, len(all_elements)):
            element = all_elements[i]

            # 检查是否是strong元素（内容）
            if element.name == 'strong':
                text = element.get_text(strip=True)
                logger.debug(f"检查strong元素: '{text[:50]}...'")

                # 跳过分类标题和噪音内容
                if not self._is_category_title(text) and not self._is_noise_content(text) and len(text) >= Config.MIN_CONTENT_LENGTH:

                    # 检查是否是分组标题
                    if self._is_group_title(text):
                        logger.debug(f"识别到分组标题: '{text[:30]}...'")

                        # 保存之前的内容-链接对
                        if current_content and not group_mode:
                            content_link_pairs.append({
                                'content': current_content,
                                'links': current_links.copy()
                            })
                            logger.debug(f"保存内容-链接对: '{current_content[:30]}...' -> {len(current_links)} 个链接")

                        # 开始分组模式
                        group_mode = True
                        group_content = text
                        current_content = text
                        current_links = []
                        logger.debug(f"开始分组模式: '{text[:30]}...'")

                    elif group_mode:
                        # 在分组模式下，跳过详细列表项
                        if self._is_detail_list_item(text):
                            logger.debug(f"分组模式下跳过详细项: '{text[:30]}...'")
                            continue
                        else:
                            # 如果不是详细列表项，可能是新的独立内容，结束分组模式
                            logger.debug(f"结束分组模式，开始新内容: '{text[:30]}...'")
                            group_mode = False

                            # 保存分组内容
                            if group_content and current_links:
                                content_link_pairs.append({
                                    'content': group_content,
                                    'links': current_links.copy()
                                })
                                logger.debug(f"保存分组内容-链接对: '{group_content[:30]}...' -> {len(current_links)} 个链接")

                            # 开始新的内容
                            current_content = text
                            current_links = []

                    else:
                        # 普通内容处理
                        # 保存之前的内容-链接对
                        if current_content:
                            content_link_pairs.append({
                                'content': current_content,
                                'links': current_links.copy()
                            })
                            logger.debug(f"保存内容-链接对: '{current_content[:30]}...' -> {len(current_links)} 个链接")

                        # 开始新的内容
                        current_content = text
                        current_links = []
                        logger.debug(f"开始新内容: '{text[:30]}...'")

            # 检查是否是链接元素
            elif element.name == 'a':
                href = element.get('href', '')
                if href and (href.startswith('https://pan.quark.cn') or href.startswith('https://pan.xunlei.com')):
                    link_type, name = self._identify_link_type(href)
                    if link_type:
                        link_info = {
                            'url': href,
                            'type': link_type,
                            'name': name,
                            'text': element.get_text(strip=True)
                        }
                        current_links.append(link_info)
                        logger.debug(f"添加链接到当前内容: {name} - {href}")

                        # 如果在分组模式下找到链接，准备结束分组模式
                        if group_mode and current_links:
                            logger.debug(f"分组模式下找到链接: '{group_content[:30]}...'")

            # 如果遇到下一个标题，停止处理
            elif element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                next_title = element.get_text(strip=True)
                if self._is_category_title(next_title) and next_title != update_heading.get_text(strip=True):
                    logger.debug(f"遇到下一个标题，停止处理: '{next_title}'")
                    break

        # 保存最后一个内容-链接对
        if current_content:
            content_link_pairs.append({
                'content': current_content,
                'links': current_links.copy()
            })
            logger.debug(f"保存最后的内容-链接对: '{current_content[:30]}...' -> {len(current_links)} 个链接")

        logger.info(f"基于DOM顺序找到 {len(content_link_pairs)} 个内容-链接对")

        # 调试：显示所有找到的配对
        for i, pair in enumerate(content_link_pairs, 1):
            logger.info(f"配对 {i}: '{pair['content'][:40]}...' -> {len(pair['links'])} 个链接")

        # 将匹配结果应用到items，同时过滤掉详细列表项
        matched_items = []
        for item in items:
            item_text = item.get('text', '')

            # 跳过详细列表项
            if self._is_detail_list_item(item_text):
                logger.debug(f"跳过详细列表项: '{item_text[:30]}...'")
                continue

            # 尝试匹配链接
            matched = False
            for pair in content_link_pairs:
                if item_text == pair['content']:
                    item['links'] = pair['links']
                    matched_items.append(item)
                    logger.info(f"为内容 '{item_text[:30]}...' 匹配到 {len(pair['links'])} 个链接")
                    matched = True
                    break

            # 如果没有匹配到链接，但不是详细列表项，也保留
            if not matched:
                matched_items.append(item)
                logger.debug(f"保留无链接的内容: '{item_text[:30]}...'")

        # 更新items列表
        items.clear()
        items.extend(matched_items)

    def _simple_sequential_assignment(self, items: List[Dict], all_links: List[Dict]):
        """简单的顺序分配方法（备用）"""
        valid_items = [item for item in items if not item.get('links')]

        if len(all_links) >= len(valid_items):
            links_per_item = len(all_links) // len(valid_items)
            for i, item in enumerate(valid_items):
                start_index = i * links_per_item
                end_index = start_index + links_per_item
                if i == len(valid_items) - 1:  # 最后一个项目获得剩余所有链接
                    end_index = len(all_links)

                item['links'] = all_links[start_index:end_index]
                logger.info(f"为内容 '{item['text'][:30]}...' 分配了 {len(item['links'])} 个链接")
        else:
            logger.warning(f"链接数量({len(all_links)})不足以为所有内容项({len(valid_items)})分配")

    def _is_link_related_to_content(self, content_text: str, link_info: Dict) -> bool:
        """判断链接是否与内容相关"""
        # 如果链接文本是"夸克"或"迅雷"等，认为是相关的
        link_text = link_info.get('text', '').lower()
        if link_text in ['夸克', '迅雷', '百度', '阿里', '蓝奏']:
            return True

        # 如果链接没有文本（直接URL），也认为是相关的
        if not link_text or link_text.startswith('http'):
            return True

        # 其他情况暂时认为不相关
        return False

    def _find_content_links_by_position(self, soup: BeautifulSoup) -> Dict[str, List[Dict]]:
        """基于位置关系查找内容和链接的对应关系"""
        content_links_map = {}

        # 调试DOM结构
        self._debug_dom_structure(soup)

        # 查找所有strong元素（内容项）
        strong_elements = soup.find_all('strong')

        for strong in strong_elements:
            text = strong.get_text(strip=True)

            # 跳过分类标题和噪音内容
            if self._is_category_title(text) or self._is_noise_content(text):
                continue

            if len(text) < Config.MIN_CONTENT_LENGTH:
                continue

            # 多种策略查找相关链接
            links = []

            # 策略1: 查找紧跟的兄弟元素
            next_element = strong.next_sibling
            search_count = 0

            while next_element and search_count < 10:  # 扩大搜索范围
                if hasattr(next_element, 'name') and next_element.name:
                    # 如果遇到下一个strong，停止搜索
                    if next_element.name == 'strong':
                        break

                    # 查找链接
                    if hasattr(next_element, 'find_all'):
                        link_elements = next_element.find_all('a', href=True)

                        for link in link_elements:
                            href = link.get('href', '')
                            link_text = link.get_text(strip=True)

                            if href:
                                link_type, name = self._identify_link_type(href)
                                if link_type:
                                    links.append({
                                        'url': href,
                                        'type': link_type,
                                        'name': name
                                    })
                                    logger.debug(f"找到链接: {link_text} -> {href}")

                next_element = next_element.next_sibling
                search_count += 1

            # 策略2: 如果没找到，在父级容器中查找
            if not links and strong.parent:
                parent_links = strong.parent.find_all('a', href=True)
                for link in parent_links:
                    href = link.get('href', '')
                    link_text = link.get_text(strip=True)

                    if href and link_text.lower() in ['夸克', '迅雷', '百度', '阿里', '蓝奏']:
                        link_type, name = self._identify_link_type(href)
                        if link_type:
                            links.append({
                                'url': href,
                                'type': link_type,
                                'name': name
                            })
                            if len(links) >= 2:  # 限制数量
                                break

            if links:
                content_links_map[text] = links
                logger.info(f"为内容 '{text[:30]}...' 找到 {len(links)} 个链接")

        return content_links_map

    def _debug_dom_structure(self, soup: BeautifulSoup):
        """调试DOM结构"""
        logger.info("=== DOM结构调试 ===")

        # 查找所有strong元素
        strong_elements = soup.find_all('strong')
        logger.info(f"找到 {len(strong_elements)} 个strong元素")

        for i, strong in enumerate(strong_elements, 1):  # 调试所有strong元素
            text = strong.get_text(strip=True)
            logger.info(f"Strong[{i}]: {text[:50]}...")

            # 查看下一个兄弟元素
            next_sibling = strong.next_sibling
            sibling_count = 0
            found_links = False

            logger.info(f"  查找兄弟元素...")

            while next_sibling and sibling_count < 5:
                if hasattr(next_sibling, 'name') and next_sibling.name:
                    sibling_text = next_sibling.get_text(strip=True)[:30]
                    logger.info(f"  -> 兄弟[{sibling_count}]: <{next_sibling.name}> '{sibling_text}...'")

                    # 查看是否包含链接
                    if hasattr(next_sibling, 'find_all'):
                        links = next_sibling.find_all('a', href=True)
                        if links:
                            logger.info(f"    ✅ 包含 {len(links)} 个链接:")
                            for link in links:
                                link_text = link.get_text(strip=True)
                                href = link.get('href', '')
                                if any(domain in href for domain in ['pan.quark.cn', 'pan.xunlei.com']):
                                    logger.info(f"      - 🔗 {link_text}: {href[:50]}...")
                                    found_links = True
                elif next_sibling:
                    text_content = str(next_sibling).strip()[:30]
                    if text_content:
                        logger.info(f"  -> 兄弟[{sibling_count}]: 文本节点: '{text_content}...'")

                next_sibling = next_sibling.next_sibling
                sibling_count += 1

            if not found_links:
                logger.info(f"  ❌ 未在兄弟元素中找到网盘链接")

            logger.info("  ---")

        # 查找所有网盘链接
        all_links = soup.find_all('a', href=True)
        netdisk_links = []

        for link in all_links:
            href = link.get('href', '')
            if any(domain in href for domain in ['pan.quark.cn', 'pan.xunlei.com', 'pan.baidu.com']):
                netdisk_links.append({
                    'text': link.get_text(strip=True),
                    'href': href,
                    'parent': link.parent.name if link.parent else None
                })

        logger.info(f"找到 {len(netdisk_links)} 个网盘链接:")
        for link in netdisk_links[:10]:  # 只显示前10个
            logger.info(f"  - '{link['text']}' -> {link['href'][:50]}... (父元素: {link['parent']})")

        logger.info("=== DOM结构调试结束 ===")
    
    def _is_noise_content(self, text: str) -> bool:
        """判断是否为噪音内容"""
        # 过滤空白内容
        if not text or re.match(r'^\s*$', text):
            return True

        # 过滤过短的内容
        if len(text) < 3:
            return True

        # 过滤过长的内容（可能是整个页面的文本）
        if len(text) > 1000:
            return True

        # 使用配置文件中的噪音模式（但更宽松）
        for pattern in Config.NOISE_PATTERNS:
            if re.match(pattern, text):
                return True

        # 过滤纯数字或纯符号
        if re.match(r'^[\d\s\-_=+*/.,:;!?()[\]{}|\\]+$', text):
            return True

        # 过滤常见的UI元素文本
        ui_texts = [
            '确定', '取消', '保存', '编辑', '删除', '返回', '刷新', '加载', '登录', '注册',
            '分享', '立即登录', '仅查看', '创建', '更新', '搜索', '查看', '下载'
        ]
        if text.strip() in ui_texts:
            return True

        # 过滤包含大量UI元素的文本
        if re.search(r'(登录.*分享|仅查看.*立即登录|创建.*更新)', text):
            return True

        return False

    def _is_group_title(self, text: str) -> bool:
        """判断是否是分组标题（如【付费短剧】、【软件分享】等）"""
        group_patterns = [
            r'【.*】.*更新.*[部个]',  # 【xxx】更新xx部/个
            r'.*【.*】.*更新',  # xxx【xxx】更新
            r'【.*短剧.*】',  # 【xxx短剧xxx】
            r'【.*软件.*】',  # 【xxx软件xxx】
            r'【.*资源.*】',  # 【xxx资源xxx】
        ]

        for pattern in group_patterns:
            if re.search(pattern, text):
                return True

        return False

    def _is_detail_list_item(self, text: str) -> bool:
        """判断是否是详细列表项（如 01.xxx、02.xxx 或 xxx/xxx/xxx）"""
        detail_patterns = [
            r'^\d+[\.\-]',  # 01. 或 01-
            r'^[^/]+/[^/]+/',  # xxx/xxx/ 格式
            r'^\d+[\.\-].*\(\d+集\)',  # 01.xxx(79集)
        ]

        for pattern in detail_patterns:
            if re.match(pattern, text):
                return True

        return False

    def _extract_content_items(self, items: List[Dict]) -> List[Dict]:
        """提取真正的内容项，过滤UI元素"""
        content_items = []

        for item in items:
            text = item.get('text', '')

            # 跳过明显的UI元素组合
            if re.search(r'^[0-9\s]*仅查看.*分享.*立即登录', text):
                continue

            # 跳过只包含数字和符号的内容
            if re.match(r'^[0-9\s\-_=+*/.,:;!?()[\]{}|\\]+$', text):
                continue

            # 跳过过短的内容
            if len(text.strip()) < 8:
                continue

            # 保留有意义的内容
            content_items.append(item)

        return content_items

    def _is_category_title(self, text: str) -> bool:
        """判断是否为分类标题"""
        # 使用配置文件中的分类模式
        for pattern in Config.CATEGORY_PATTERNS:
            if re.match(pattern, text):
                return True
        return False
    
    def _extract_links(self, element) -> List[Dict]:
        """提取元素中的链接"""
        links = []

        # 只搜索当前元素和直接父级，避免获取整个页面的链接
        search_elements = [element]
        if element.parent:
            search_elements.append(element.parent)

        # 网盘链接模式
        link_patterns = {
            'quark': {
                'patterns': [r'pan\.quark\.cn'],
                'name': '夸克网盘'
            },
            'xunlei': {
                'patterns': [r'pan\.xunlei\.com'],
                'name': '迅雷网盘'
            },
            'baidu': {
                'patterns': [r'pan\.baidu\.com'],
                'name': '百度网盘'
            },
            'aliyun': {
                'patterns': [r'aliyundrive\.com', r'alipan\.com'],
                'name': '阿里云盘'
            },
            'lanzou': {
                'patterns': [r'lanzou[a-z]*\.com'],
                'name': '蓝奏云'
            }
        }

        for search_element in search_elements:
            # 查找链接元素
            link_elements = search_element.find_all('a', href=True)

            for link in link_elements:
                href = link.get('href', '')
                if not href:
                    continue

                # 匹配网盘类型
                for link_type, config in link_patterns.items():
                    for pattern in config['patterns']:
                        if re.search(pattern, href, re.IGNORECASE):
                            # 避免重复添加相同链接
                            if not any(existing['url'] == href for existing in links):
                                links.append({
                                    'url': href,
                                    'type': link_type,
                                    'name': config['name']
                                })
                            break

        return links

    def _extract_nearby_links(self, element, text: str) -> List[Dict]:
        """提取与当前文本相关的链接"""
        links = []

        # 如果文本本身就是链接，直接提取
        if re.search(r'https?://pan\.(quark|xunlei|baidu)\.', text):
            url_match = re.search(r'(https?://[^\s]+)', text)
            if url_match:
                url = url_match.group(1).rstrip('#')
                link_type, name = self._identify_link_type(url)
                if link_type:
                    links.append({
                        'url': url,
                        'type': link_type,
                        'name': name
                    })

        # 在当前元素后面查找紧邻的链接
        else:
            # 查找下一个兄弟元素
            next_sibling = element.next_sibling
            search_count = 0

            while next_sibling and search_count < 5:  # 最多搜索5个后续元素
                if hasattr(next_sibling, 'name'):
                    # 如果是链接元素
                    if next_sibling.name == 'a' and next_sibling.get('href'):
                        href = next_sibling.get('href')
                        link_type, name = self._identify_link_type(href)
                        if link_type:
                            links.append({
                                'url': href,
                                'type': link_type,
                                'name': name
                            })

                    # 如果是包含链接的容器
                    elif next_sibling.name in ['div', 'span', 'p']:
                        link_elements = next_sibling.find_all('a', href=True)
                        for link in link_elements:
                            href = link.get('href', '')
                            if href:
                                link_type, name = self._identify_link_type(href)
                                if link_type:
                                    if not any(existing['url'] == href for existing in links):
                                        links.append({
                                            'url': href,
                                            'type': link_type,
                                            'name': name
                                        })

                    # 如果遇到下一个内容项（strong标签），停止搜索
                    elif next_sibling.name == 'strong':
                        break

                    search_count += 1

                next_sibling = next_sibling.next_sibling

            # 如果还没找到链接，在父级容器中搜索
            if not links and element.parent:
                parent_links = element.parent.find_all('a', href=True)
                for link in parent_links:
                    href = link.get('href', '')
                    if href:
                        link_type, name = self._identify_link_type(href)
                        if link_type:
                            # 检查链接是否在当前元素附近
                            link_text = link.get_text(strip=True).lower()
                            if link_text in ['夸克', '迅雷', '百度', '阿里', '蓝奏'] or not link_text:
                                links.append({
                                    'url': href,
                                    'type': link_type,
                                    'name': name
                                })
                                if len(links) >= 2:  # 限制数量
                                    break

        return links

    def _identify_link_type(self, url: str) -> tuple:
        """识别链接类型"""
        link_patterns = {
            'quark': ('夸克网盘', [r'pan\.quark\.cn']),
            'xunlei': ('迅雷网盘', [r'pan\.xunlei\.com']),
            'baidu': ('百度网盘', [r'pan\.baidu\.com']),
            'aliyun': ('阿里云盘', [r'aliyundrive\.com', r'alipan\.com']),
            'lanzou': ('蓝奏云', [r'lanzou[a-z]*\.com']),
        }

        for link_type, (name, patterns) in link_patterns.items():
            for pattern in patterns:
                if re.search(pattern, url, re.IGNORECASE):
                    return link_type, name

        return None, None
    
    def _classify_content(self, text: str) -> str:
        """内容分类"""
        if not Config.ENABLE_CLASSIFICATION:
            return "general"

        text_lower = text.lower()

        # 电影分类
        movie_patterns = [
            r'\[202[0-9]\]',  # 年份标识
            r'4K|高码|蓝光|BD',  # 质量标识
            r'电影|影片',  # 直接标识
            r'\.mp4|\.mkv|\.avi',  # 视频格式
        ]
        for pattern in movie_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return "movie"

        # 电视剧分类
        tv_patterns = [
            r'第.*季',
            r'全\d+集',
            r'更新至\d+',
            r'连续剧|电视剧',
            r'S\d+E\d+',  # 季集标识
        ]
        for pattern in tv_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return "tv_series"

        # 短剧分类
        short_drama_patterns = [
            r'\(\d+集\)',
            r'短剧',
            r'微剧',
            r'网剧.*\d+集',
        ]
        for pattern in short_drama_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return "short_drama"

        # 动漫分类
        anime_patterns = [
            r'动漫|动画',
            r'番剧',
            r'OVA|OAD',
        ]
        for pattern in anime_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return "anime"

        # 综艺分类
        variety_patterns = [
            r'综艺',
            r'真人秀',
            r'脱口秀',
        ]
        for pattern in variety_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return "variety"

        # 软件分类
        software_patterns = [
            r'软件|应用|APP',
            r'\.exe|\.apk|\.dmg',
            r'破解版|绿色版|便携版',
        ]
        for pattern in software_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return "software"

        return "general"
    
    def _deduplicate_items(self, items: List[Dict]) -> List[Dict]:
        """去重和合并相似内容"""
        if not items:
            return []

        # 按分类分组
        category_groups = {}
        for item in items:
            category = item.get('category', '未分类')
            if category not in category_groups:
                category_groups[category] = []
            category_groups[category].append(item)

        unique_items = []

        for category, group_items in category_groups.items():
            seen_texts = set()
            category_items = []

            for item in group_items:
                text = item['text']

                # 过滤明显的重复和碎片
                if len(text) < 10:  # 太短的内容跳过
                    continue

                # 检查是否与已有内容重复或包含
                is_duplicate = False
                text_lower = text.lower()

                for seen_text in seen_texts:
                    # 如果当前文本是已有文本的子集，跳过
                    if text_lower in seen_text.lower():
                        is_duplicate = True
                        break
                    # 如果已有文本是当前文本的子集，替换
                    elif seen_text.lower() in text_lower:
                        # 找到并替换旧项
                        for i, existing_item in enumerate(category_items):
                            if existing_item['text'].lower() == seen_text.lower():
                                category_items[i] = item
                                seen_texts.remove(seen_text)
                                seen_texts.add(text_lower)
                                is_duplicate = True
                                break
                        break

                if not is_duplicate:
                    seen_texts.add(text_lower)
                    category_items.append(item)

            unique_items.extend(category_items)

        return unique_items
