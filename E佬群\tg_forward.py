#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import asyncio
from typing import List, Dict, Any
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.errors import FloodWaitError, SessionPasswordNeededError, AuthKeyError
from config import config
from logger import get_logger
from performance import async_timed, performance_monitor, BatchProcessor
from exceptions import (
    TelegramError,
    FileProcessingError,
    ConfigurationError,
    handle_exceptions,
    handle_async_exceptions,
    async_retry,
    ErrorContext
)

# 获取模块日志器
logger = get_logger(__name__)

# 读取资源文件
@handle_exceptions(
    exceptions=[FileNotFoundError, json.JSONDecodeError, IOError],
    default_return=[],
    log_error=True,
    custom_message="读取资源文件失败"
)
def load_resources(file_path: str) -> List[Dict[str, Any]]:
    """
    读取资源文件

    Args:
        file_path: 资源文件路径

    Returns:
        List[Dict[str, Any]]: 资源列表

    Raises:
        FileProcessingError: 文件处理错误
    """
    from pathlib import Path

    if not file_path:
        raise FileProcessingError("资源文件路径不能为空")

    file_path = Path(file_path)
    if not file_path.exists():
        raise FileProcessingError(f"资源文件不存在: {file_path}")

    if not file_path.is_file():
        raise FileProcessingError(f"路径不是文件: {file_path}")

    try:
        logger.info(f"正在读取资源文件: {file_path}")

        with ErrorContext("读取JSON文件", logger_name=__name__) as ctx:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

        if ctx.exception:
            raise FileProcessingError(f"读取资源文件失败: {file_path}", original_error=ctx.exception)

        # 支持新旧两种格式
        if isinstance(data, list):
            # 旧格式：直接是资源列表
            resources = data
        elif isinstance(data, dict) and 'resources' in data:
            # 新格式：包含元数据的字典
            resources = data['resources']
            # 转换为旧格式以保持兼容性
            legacy_resources = []
            for resource in resources:
                if isinstance(resource, dict) and 'title' in resource and 'links' in resource:
                    # 提取链接URL
                    links = []
                    for link in resource['links']:
                        if isinstance(link, dict) and 'url' in link:
                            links.append(link['url'])
                        elif isinstance(link, str):
                            links.append(link)

                    legacy_resources.append({
                        'title': resource['title'],
                        'links': links
                    })
            resources = legacy_resources
        else:
            raise FileProcessingError(f"资源文件格式错误，无法识别格式: {file_path}")

        logger.info(f"成功读取 {len(resources)} 个资源项")
        return resources

    except FileProcessingError:
        raise
    except Exception as e:
        raise FileProcessingError(f"读取资源文件时发生未预期的错误: {file_path}", original_error=e)

# 格式化消息
def format_message(resource: Dict[str, Any]) -> str:
    title = resource.get('title', '未知标题')
    links = resource.get('links', [])
    
    # 格式化链接
    links_text = '\n'.join(links)
    
    # 构建完整消息 - 移除标题和链接之间的空行
    message = f"**{title}**\n{links_text}"
    return message

# 处理目标频道标识符
def process_channel_identifier(channel):
    """处理各种格式的频道标识符"""
    if channel.startswith('@'):
        # 去掉开头的@符号
        return channel[1:]
    
    try:
        # 尝试将其解析为整数ID
        return int(channel)
    except ValueError:
        # 如果不是整数，则按原样返回（视为用户名）
        return channel

@async_timed(performance_monitor)
async def main():
    # 获取配置
    try:
        api_id, api_hash, string_session, target_channel = config.get_required_tg_config()
    except ValueError as e:
        logger.error(str(e))
        return

    send_delay = config.send_delay
    resource_file = config.resource_file
    
    # 处理目标频道标识符
    channel_identifier = process_channel_identifier(target_channel)
    
    # 读取资源
    resources = load_resources(resource_file)
    if not resources:
        logger.error(f"没有找到资源或资源文件 {resource_file} 为空")
        return

    logger.info(f"找到 {len(resources)} 个资源项准备转发到频道: {target_channel}")
    logger.info(f"发送延迟设置: {send_delay} 秒")
    
    # 初始化TelegramClient
    logger.info("正在初始化Telegram客户端...")
    client = TelegramClient(StringSession(string_session), int(api_id), api_hash)
    await client.start()
    logger.info("Telegram客户端初始化成功")

    try:
        # 获取频道信息
        logger.info(f"正在获取频道信息: {channel_identifier}")
        try:
            entity = await client.get_entity(channel_identifier)
            entity_name = getattr(entity, 'title', getattr(entity, 'username', str(channel_identifier)))
            logger.info(f"成功连接到频道: {entity_name}")
        except Exception as e:
            logger.error(f"获取频道信息失败: {str(e)}")
            logger.warning("将尝试直接使用提供的频道标识符发送消息")
            entity = channel_identifier
        
        # 发送消息
        logger.info("开始发送消息...")
        success_count = 0
        failed_count = 0

        for i, resource in enumerate(resources, 1):
            message = format_message(resource)
            title = resource.get('title', '未知标题')

            try:
                logger.debug(f"[{i}/{len(resources)}] 正在发送: {title[:50]}...")
                await client.send_message(entity, message, parse_mode='md')
                logger.info(f"[{i}/{len(resources)}] 成功发送: {title[:50]}...")
                success_count += 1

                # 延迟避免触发限制
                if i < len(resources):  # 最后一条消息不需要延迟
                    await asyncio.sleep(send_delay)

            except FloodWaitError as e:
                wait_time = e.seconds
                logger.warning(f"触发频率限制，需等待 {wait_time} 秒")
                await asyncio.sleep(wait_time)

                # 重试发送
                try:
                    await client.send_message(entity, message, parse_mode='md')
                    logger.info(f"[{i}/{len(resources)}] (重试)成功发送: {title[:50]}...")
                    success_count += 1
                except Exception as retry_e:
                    logger.error(f"[{i}/{len(resources)}] 重试发送失败: {str(retry_e)}")
                    failed_count += 1

            except Exception as e:
                logger.error(f"[{i}/{len(resources)}] 发送消息失败: {str(e)}")
                failed_count += 1

        # 发送完成统计
        if failed_count == 0:
            logger.info(f"🎉 转发完成！成功发送 {success_count} 条消息")
        else:
            logger.warning(f"转发完成！成功: {success_count}, 失败: {failed_count}, 总计: {len(resources)}")
    finally:
        # 关闭客户端
        logger.info("正在关闭Telegram客户端...")
        await client.disconnect()
        logger.info("Telegram客户端已关闭")

if __name__ == "__main__":
    asyncio.run(main()) 