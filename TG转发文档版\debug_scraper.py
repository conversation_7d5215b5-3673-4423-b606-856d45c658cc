#!/usr/bin/env python3
"""
调试版爬取器 - 用于测试和完善文档内容的爬取与整理
"""

import asyncio
import logging
import json
from pathlib import Path
from scraper import KDocsScraper
from config import Config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def debug_scrape():
    """调试爬取功能"""
    print("🔍 开始调试爬取功能...")
    print(f"📄 目标文档: {Config.KDOCS_URL}")
    print(f"⚙️ 最小内容长度: {Config.MIN_CONTENT_LENGTH}")
    print(f"🏷️ 启用分类: {Config.ENABLE_CLASSIFICATION}")
    print("-" * 60)
    
    async with KDocsScraper() as scraper:
        # 爬取内容
        items = await scraper.scrape_document(Config.KDOCS_URL)
        
        print(f"\n📊 爬取结果统计:")
        print(f"总条数: {len(items)}")
        
        # 按分类统计
        categories = {}
        content_types = {}
        link_stats = {'有链接': 0, '无链接': 0}
        
        for item in items:
            # 分类统计
            category = item.get('category', '未知')
            categories[category] = categories.get(category, 0) + 1
            
            # 内容类型统计
            content_type = item.get('type', '未知')
            content_types[content_type] = content_types.get(content_type, 0) + 1
            
            # 链接统计
            if item.get('links'):
                link_stats['有链接'] += 1
            else:
                link_stats['无链接'] += 1
        
        print(f"\n📂 分类统计:")
        for category, count in categories.items():
            print(f"  {category}: {count} 条")
        
        print(f"\n🏷️ 内容类型统计:")
        for content_type, count in content_types.items():
            print(f"  {content_type}: {count} 条")
        
        print(f"\n🔗 链接统计:")
        for link_type, count in link_stats.items():
            print(f"  {link_type}: {count} 条")
        
        # 详细内容展示
        print(f"\n📝 详细内容:")
        print("=" * 80)
        
        for i, item in enumerate(items, 1):
            print(f"\n[{i}] 分类: {item.get('category', '未知')}")
            print(f"    类型: {item.get('type', '未知')}")
            print(f"    内容: {item.get('text', '')[:100]}...")
            
            links = item.get('links', [])
            if links:
                print(f"    链接: {len(links)} 个")
                for j, link in enumerate(links, 1):
                    print(f"      [{j}] {link.get('name', '未知')}: {link.get('url', '')}")
            else:
                print(f"    链接: 无")
            
            print("-" * 40)
        
        # 保存调试数据
        debug_file = Path(__file__).parent / 'debug_output.json'
        with open(debug_file, 'w', encoding='utf-8') as f:
            json.dump(items, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 调试数据已保存到: {debug_file}")
        
        return items

async def test_specific_patterns():
    """测试特定的模式匹配"""
    print("\n🧪 测试模式匹配...")
    
    # 测试分类标题模式
    test_titles = [
        "2025.05.22 周四更新的",
        "2024.12.31 周二更新",
        "热门资源",
        "普通文本",
        "2025.1.1 周三更新的内容"
    ]
    
    from scraper import KDocsScraper
    scraper = KDocsScraper()
    
    print("\n📋 分类标题测试:")
    for title in test_titles:
        is_category = scraper._is_category_title(title)
        print(f"  '{title}' -> {'✅ 分类标题' if is_category else '❌ 普通文本'}")
    
    # 测试内容分类
    test_contents = [
        "《阿凡达2》[2024]4K高码版",
        "《庆余年》第二季 全36集",
        "《霸道总裁爱上我》(30集)短剧",
        "普通的文档内容",
        "更新至第10集"
    ]
    
    print("\n🏷️ 内容分类测试:")
    for content in test_contents:
        content_type = scraper._classify_content(content)
        print(f"  '{content}' -> {content_type}")

if __name__ == "__main__":
    try:
        # 运行调试
        asyncio.run(debug_scrape())
        
        # 测试模式匹配
        asyncio.run(test_specific_patterns())
        
        print("\n🎉 调试完成！")
        
    except KeyboardInterrupt:
        print("\n👋 调试被用户中断")
    except Exception as e:
        print(f"\n💥 调试出错: {e}")
        import traceback
        traceback.print_exc()
