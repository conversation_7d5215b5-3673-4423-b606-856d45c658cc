import asyncio
import logging
import os
from datetime import datetime, timezone, timedelta
from typing import List, Dict, Optional, Any
from telethon import TelegramClient
from telethon.sessions import StringSession
from telethon.errors import FloodWaitError, RPCError

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    print("警告: 未安装 python-dotenv，请运行: pip install python-dotenv")
    print("或者手动设置环境变量")

# ==================== 配置区域 ====================
# 从环境变量读取TG API配置
def load_api_config() -> int:
    """安全加载API配置"""
    try:
        api_id_str = os.getenv('TG_API_ID', '0')
        api_id = int(api_id_str)
        if api_id == 0:
            raise ValueError("API_ID 不能为0")
        return api_id
    except ValueError as e:
        print(f"❌ API_ID 配置错误: {e}")
        print(f"   当前值: {os.getenv('TG_API_ID', 'None')}")
        print("   请在.env文件中设置正确的 TG_API_ID")
        exit(1)

API_ID = load_api_config()
API_HASH = os.getenv('TG_API_HASH', '')
STRING_SESSION = os.getenv('TG_STRING_SESSION', '')

# 代理设置（可选）
PROXY_URL = os.getenv('TG_PROXY', '')
PROXY = None
if PROXY_URL:
    # 解析代理URL，例如：socks5://127.0.0.1:7897
    try:
        import socks
        if PROXY_URL.startswith('socks5://'):
            proxy_parts = PROXY_URL.replace('socks5://', '').split(':')
            PROXY = (socks.SOCKS5, proxy_parts[0], int(proxy_parts[1]))
        elif PROXY_URL.startswith('http://'):
            proxy_parts = PROXY_URL.replace('http://', '').split(':')
            PROXY = (socks.HTTP, proxy_parts[0], int(proxy_parts[1]))
    except Exception as e:
        print(f"代理配置解析失败: {e}")
        PROXY = None

# 日志级别配置
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO').upper()

# 可配置参数
DEFAULT_BATCH_SIZE = int(os.getenv('TG_BATCH_SIZE', '100'))
DEFAULT_TIMEZONE_OFFSET = int(os.getenv('TG_TIMEZONE_OFFSET', '8'))  # 默认中国时区
MAX_RETRY_COUNT = int(os.getenv('TG_MAX_RETRIES', '3'))
FLOOD_WAIT_THRESHOLD = int(os.getenv('TG_FLOOD_WAIT_THRESHOLD', '300'))  # 5分钟

# ==================== 主程序 ====================

class TGCleaner:
    @staticmethod
    def _validate_time_format(time_str: str) -> bool:
        """验证时间格式是否正确"""
        try:
            datetime.strptime(time_str, "%Y-%m-%d %H:%M")
            return True
        except ValueError:
            return False

    @staticmethod
    def _validate_chat_name(chat_name: str) -> bool:
        """验证频道/群组名称格式"""
        if not chat_name or not chat_name.strip():
            return False
        # 基本格式检查：可以是@username、数字ID或普通名称
        chat_name = chat_name.strip()
        if chat_name.startswith('@') and len(chat_name) > 1:
            return True
        if chat_name.isdigit() or (chat_name.startswith('-') and chat_name[1:].isdigit()):
            return True
        if len(chat_name) >= 2:  # 普通名称至少2个字符
            return True
        return False

    @staticmethod
    def _validate_keywords(keywords: List[str]) -> bool:
        """验证关键词列表"""
        if not keywords:
            return False
        for keyword in keywords:
            if not keyword.strip():
                return False
            if len(keyword.strip()) < 1:
                return False
        return True

    @staticmethod
    async def _async_input(prompt: str) -> str:
        """异步输入函数，避免阻塞事件循环"""
        try:
            # Python 3.9+ 支持 asyncio.to_thread
            return await asyncio.to_thread(input, prompt)
        except AttributeError:
            # Python < 3.9 的回退方案
            import concurrent.futures
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor() as executor:
                return await loop.run_in_executor(executor, input, prompt)

    def __init__(self, api_id: int, api_hash: str, string_session: str, proxy: Optional[tuple] = None) -> None:
        self.api_id = api_id
        self.api_hash = api_hash
        self.string_session = string_session
        self.client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy)

        # 配置会话参数
        self.client.flood_sleep_threshold = 60  # 60秒内自动等待flood wait

        # 配置日志 - 使用全局配置的logger
        self.logger = logging.getLogger(__name__)

        # 可配置的时区偏移
        self.timezone_offset = timedelta(hours=DEFAULT_TIMEZONE_OFFSET)

        # 可配置的批量大小和重试参数
        self.default_batch_size = DEFAULT_BATCH_SIZE
        self.max_retries = MAX_RETRY_COUNT
        self.flood_wait_threshold = FLOOD_WAIT_THRESHOLD

    async def _safe_delete_messages(self, chat: Any, message_ids: List[int], max_retries: Optional[int] = None) -> tuple[int, List[int]]:
        """安全删除消息，包含FloodWaitError处理和重试机制

        Returns:
            tuple: (成功删除的消息数量, 跳过的消息ID列表)
        """
        if not message_ids:
            return 0, []

        if max_retries is None:
            max_retries = self.max_retries

        # 自动切片处理大批量消息（Telegram API限制为100条）
        if len(message_ids) > 100:
            self.logger.info(f"消息ID数量 {len(message_ids)} 超过API限制100，自动切片处理")
            total_deleted = 0
            all_skipped_ids = []
            for i in range(0, len(message_ids), 100):
                chunk = message_ids[i:i + 100]
                chunk_deleted, chunk_skipped = await self._safe_delete_messages(chat, chunk, max_retries)
                total_deleted += chunk_deleted
                all_skipped_ids.extend(chunk_skipped)
                self.logger.debug(f"切片 {i//100 + 1}: 删除 {chunk_deleted} 条消息，跳过 {len(chunk_skipped)} 条")
            return total_deleted, all_skipped_ids

        for attempt in range(max_retries):
            try:
                await self.client.delete_messages(chat, message_ids)
                return len(message_ids), []  # 成功删除，无跳过
            except FloodWaitError as e:
                wait_time = e.seconds
                self.logger.warning(f"遇到频率限制，需等待 {wait_time} 秒 (尝试 {attempt + 1}/{max_retries})")

                # 如果等待时间过长，记录跳过的消息ID
                if wait_time > self.flood_wait_threshold:
                    self.logger.error(f"FloodWait时间过长 ({wait_time}秒，阈值{self.flood_wait_threshold}秒)，跳过此批次")
                    return 0, message_ids  # 跳过所有消息

                if attempt < max_retries - 1:  # 不是最后一次尝试
                    await asyncio.sleep(wait_time)
                else:
                    self.logger.error(f"达到最大重试次数，跳过此批次消息删除")
                    return 0, message_ids  # 跳过所有消息
            except RPCError as e:
                # 指数退避策略
                wait_time = min(2 ** attempt, 30)  # 最大等待30秒
                self.logger.error(f"删除消息时发生RPC错误: {e} (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    return 0, message_ids  # 跳过所有消息
            except Exception as e:
                # 指数退避策略
                wait_time = min(2 ** attempt, 10)  # 最大等待10秒
                self.logger.error(f"删除消息时发生未知错误: {e} (尝试 {attempt + 1}/{max_retries})")
                if attempt < max_retries - 1:
                    self.logger.info(f"等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    return 0, message_ids  # 跳过所有消息
        return 0, message_ids  # 所有重试都失败，跳过所有消息

    async def _process_and_delete_messages(self, chat: Any, message_filter_func, batch_size: int, operation_name: str, offset_date: Optional[datetime] = None) -> int:
        """通用的消息处理和删除流程，支持性能优化的offset_date"""
        messages_to_delete = []
        deleted_count = 0
        scanned_count = 0
        total_skipped_ids = []

        if offset_date:
            print(f"开始扫描 {operation_name}（使用性能优化）...")
            self.logger.info(f"使用offset_date优化: {offset_date}")
        else:
            print(f"开始扫描 {operation_name}...")

        try:
            # 根据是否有offset_date选择不同的迭代方式
            if offset_date:
                message_iter = self.client.iter_messages(chat, offset_date=offset_date)
            else:
                message_iter = self.client.iter_messages(chat)

            async for message in message_iter:
                scanned_count += 1

                # 使用过滤函数判断是否需要删除
                if message_filter_func(message):
                    messages_to_delete.append(message.id)

                    # 批量删除
                    if len(messages_to_delete) >= batch_size:
                        try:
                            batch_deleted, skipped_ids = await self._safe_delete_messages(chat, messages_to_delete)
                            deleted_count += batch_deleted
                            total_skipped_ids.extend(skipped_ids)
                            print(f"已扫描 {scanned_count} 条，删除 {deleted_count} 条消息...")
                            self.logger.info(f"{operation_name} - 删除批次消息: {batch_deleted} 条，跳过: {len(skipped_ids)} 条")
                            messages_to_delete = []
                        except Exception as e:
                            self.logger.error(f"批量删除失败: {e}")
                            print(f"批量删除失败: {e}")
                            messages_to_delete = []

                # 每1000条消息显示进度
                if scanned_count % 1000 == 0:
                    print(f"已扫描 {scanned_count} 条消息...")

        except Exception as e:
            if offset_date:
                self.logger.warning(f"offset_date优化失败，回退到普通扫描: {e}")
                # 回退到普通扫描
                return await self._process_and_delete_messages(chat, message_filter_func, batch_size, operation_name, None)
            else:
                raise

        # 删除剩余消息
        if messages_to_delete:
            try:
                batch_deleted, skipped_ids = await self._safe_delete_messages(chat, messages_to_delete)
                deleted_count += batch_deleted
                total_skipped_ids.extend(skipped_ids)
                self.logger.info(f"{operation_name} - 删除剩余消息: {batch_deleted} 条，跳过: {len(skipped_ids)} 条")
            except Exception as e:
                self.logger.error(f"删除剩余消息失败: {e}")
                print(f"删除剩余消息失败: {e}")

        # 报告跳过的消息
        if total_skipped_ids:
            self.logger.warning(f"{operation_name} - 总计跳过 {len(total_skipped_ids)} 条消息ID: {total_skipped_ids[:10]}{'...' if len(total_skipped_ids) > 10 else ''}")
            print(f"⚠️  注意：由于网络限制跳过了 {len(total_skipped_ids)} 条消息")

        self.logger.info(f"{operation_name} 完成: 扫描 {scanned_count} 条，删除 {deleted_count} 条，跳过 {len(total_skipped_ids)} 条")
        print(f"✅ 完成！扫描 {scanned_count} 条消息，删除 {deleted_count} 条消息")
        return deleted_count

    async def get_chat_info(self, chat_name: str, quick_mode: bool = True) -> Optional[Dict[str, Any]]:
        """获取频道/群组信息"""
        try:
            self.logger.info(f"正在获取 {chat_name} 的信息...")
            chat = await self.client.get_entity(chat_name)

            # 获取消息统计 - 优化性能
            if quick_mode:
                # 快速模式：只获取最近几条消息来估算
                sample_count = 0
                latest_message_id = None
                oldest_message_id = None

                async for message in self.client.iter_messages(chat, limit=100):
                    sample_count += 1
                    if latest_message_id is None:
                        latest_message_id = message.id
                    oldest_message_id = message.id

                # 基于消息ID差值估算总消息数（不完全准确但快速）
                if latest_message_id and oldest_message_id and sample_count > 10:
                    estimated_total = f"~{latest_message_id - oldest_message_id + 1} (估算)"
                else:
                    estimated_total = f"{sample_count}+ (采样)"

                total_messages = estimated_total
            else:
                # 精确模式：遍历所有消息（慢但准确）
                total_messages = 0
                async for message in self.client.iter_messages(chat):
                    total_messages += 1
                    if total_messages >= 10000:  # 限制统计数量，避免太慢
                        break
                total_messages = f"{total_messages}+" if total_messages >= 10000 else total_messages

            chat_info = {
                'id': chat.id,
                'title': getattr(chat, 'title', chat_name),
                'username': getattr(chat, 'username', None),
                'type': 'channel' if hasattr(chat, 'broadcast') and chat.broadcast else 'group',
                'participants_count': getattr(chat, 'participants_count', 'Unknown'),
                'total_messages': total_messages,
                'created_date': getattr(chat, 'date', 'Unknown')
            }

            self.logger.info(f"成功获取 {chat_info['title']} 的信息")
            return chat_info

        except Exception as e:
            self.logger.error(f"获取聊天信息失败 {chat_name}: {e}")
            print(f"获取聊天信息失败 {chat_name}: {e}")
            return None

    async def delete_messages_in_time_range(self, chat_name: str, start_time_str: str, end_time_str: str, batch_size: Optional[int] = None) -> int:
        """删除指定时间范围内的消息"""
        if batch_size is None:
            batch_size = self.default_batch_size

        try:
            # 解析时间
            target_timezone = timezone(self.timezone_offset)
            start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M").replace(tzinfo=target_timezone)
            end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M").replace(tzinfo=target_timezone)

            self.logger.info(f"开始按时间范围删除消息: {chat_name} ({start_time_str} 到 {end_time_str})")
            chat = await self.client.get_entity(chat_name)

            # 定义时间范围过滤函数
            def time_filter(message):
                message_target_time = message.date.astimezone(target_timezone)
                return start_time <= message_target_time <= end_time

            # 使用优化的时间范围处理流程（带offset_date性能优化）
            operation_name = f"按时间范围删除 ({start_time_str} 到 {end_time_str})"
            deleted_count = await self._process_and_delete_messages(
                chat, time_filter, batch_size, operation_name, end_time
            )
            return deleted_count

        except Exception as e:
            self.logger.error(f"按时间范围删除消息失败: {e}")
            print(f"删除消息失败: {e}")
            return 0

    async def delete_all_messages(self, chat_name: str, confirm_text: str = "CONFIRM_DELETE_ALL") -> int:
        """删除频道/群组的所有消息（危险操作）"""
        print(f"⚠️  警告：即将删除 {chat_name} 的所有消息！")
        print(f"请输入确认文本 '{confirm_text}' 来确认操作：")

        user_input = (await self._async_input("")).strip()
        if user_input != confirm_text:
            print("❌ 确认文本不匹配，操作已取消")
            self.logger.info(f"用户取消删除所有消息操作: {chat_name}")
            return 0

        try:
            self.logger.warning(f"开始删除所有消息: {chat_name}")
            chat = await self.client.get_entity(chat_name)

            # 定义全部删除过滤函数（所有消息都删除）
            def all_messages_filter(message):
                return True

            # 使用通用处理流程
            operation_name = "删除所有消息"
            deleted_count = await self._process_and_delete_messages(
                chat, all_messages_filter, self.default_batch_size, operation_name
            )

            self.logger.warning(f"删除所有消息完成: {chat_name}, 共删除 {deleted_count} 条")
            return deleted_count

        except Exception as e:
            self.logger.error(f"删除所有消息失败: {e}")
            print(f"删除所有消息失败: {e}")
            return 0

    def _match_keywords(self, text: str, keywords: List[str], case_sensitive: bool = False) -> bool:
        """改进的关键词匹配函数"""
        if not text:
            return False

        search_text = text if case_sensitive else text.lower()
        search_keywords = keywords if case_sensitive else [k.lower() for k in keywords]

        return any(keyword in search_text for keyword in search_keywords)

    async def delete_messages_by_keyword(self, chat_name: str, keywords: List[str], batch_size: Optional[int] = None, case_sensitive: bool = False) -> int:
        """删除包含特定关键词的消息"""
        if batch_size is None:
            batch_size = self.default_batch_size

        try:
            self.logger.info(f"开始按关键词删除消息: {chat_name}, 关键词: {keywords}, 大小写敏感: {case_sensitive}")
            chat = await self.client.get_entity(chat_name)

            # 定义关键词过滤函数
            def keyword_filter(message):
                # 检查消息文本
                if message.message:
                    if self._match_keywords(message.message, keywords, case_sensitive):
                        return True

                # 检查媒体消息的caption
                if message.media and hasattr(message, 'text') and message.text:
                    if self._match_keywords(message.text, keywords, case_sensitive):
                        return True

                return False

            # 使用通用处理流程
            operation_name = f"按关键词删除 {keywords}"
            deleted_count = await self._process_and_delete_messages(
                chat, keyword_filter, batch_size, operation_name
            )
            return deleted_count

        except Exception as e:
            self.logger.error(f"按关键词删除消息失败: {e}")
            print(f"按关键词删除消息失败: {e}")
            return 0

    async def delete_media_messages(self, chat_name: str, media_types: Optional[List[str]] = None, batch_size: Optional[int] = None) -> int:
        """删除特定类型的媒体消息"""
        if media_types is None:
            media_types = ['photo', 'video', 'document']
        if batch_size is None:
            batch_size = self.default_batch_size
        try:
            self.logger.info(f"开始按媒体类型删除消息: {chat_name}, 类型: {media_types}")
            chat = await self.client.get_entity(chat_name)

            # 定义媒体类型过滤函数
            def media_filter(message):
                if not message.media:
                    return False

                if 'photo' in media_types and message.photo:
                    return True
                elif 'video' in media_types and message.video:
                    return True
                elif 'document' in media_types and message.document:
                    return True
                elif 'sticker' in media_types and message.sticker:
                    return True
                elif 'voice' in media_types and message.voice:
                    return True

                return False

            # 使用通用处理流程
            operation_name = f"按媒体类型删除 {media_types}"
            deleted_count = await self._process_and_delete_messages(
                chat, media_filter, batch_size, operation_name
            )
            return deleted_count

        except Exception as e:
            self.logger.error(f"删除媒体消息失败: {e}")
            print(f"删除媒体消息失败: {e}")
            return 0

    async def get_message_statistics(self, chat_name: str, limit: int = 10000) -> Optional[Dict[str, Any]]:
        """获取消息统计信息"""
        try:
            self.logger.info(f"开始获取消息统计: {chat_name}, 限制: {limit} 条")
            chat = await self.client.get_entity(chat_name)

            stats = {
                'total_messages': 0,
                'text_messages': 0,
                'media_messages': 0,
                'photo_messages': 0,
                'video_messages': 0,
                'document_messages': 0,
                'sticker_messages': 0,
                'voice_messages': 0,
                'users': set(),
                'date_range': {'earliest': None, 'latest': None}
            }

            print(f"正在分析 {chat_name} 的消息统计（最多 {limit} 条）...")

            async for message in self.client.iter_messages(chat, limit=limit):
                stats['total_messages'] += 1

                # 统计用户
                if message.sender_id:
                    stats['users'].add(message.sender_id)

                # 统计时间范围
                if stats['date_range']['earliest'] is None or message.date < stats['date_range']['earliest']:
                    stats['date_range']['earliest'] = message.date
                if stats['date_range']['latest'] is None or message.date > stats['date_range']['latest']:
                    stats['date_range']['latest'] = message.date

                # 统计消息类型
                if message.message:
                    stats['text_messages'] += 1

                if message.media:
                    stats['media_messages'] += 1

                    if message.photo:
                        stats['photo_messages'] += 1
                    elif message.video:
                        stats['video_messages'] += 1
                    elif message.document:
                        stats['document_messages'] += 1
                    elif message.sticker:
                        stats['sticker_messages'] += 1
                    elif message.voice:
                        stats['voice_messages'] += 1

                # 每1000条显示进度
                if stats['total_messages'] % 1000 == 0:
                    print(f"已分析 {stats['total_messages']} 条消息...")

            stats['unique_users'] = len(stats['users'])
            del stats['users']  # 删除用户集合，只保留数量

            self.logger.info(f"消息统计完成: 总计 {stats['total_messages']} 条消息")
            return stats

        except Exception as e:
            self.logger.error(f"获取消息统计失败: {e}")
            print(f"获取消息统计失败: {e}")
            return None

    def run_with_menu(self) -> None:
        """运行交互式菜单"""
        async def _run_menu():
            try:
                await self.client.start()
                await self.interactive_menu()
            except KeyboardInterrupt:
                print("\n👋 用户中断，正在退出...")
                self.logger.info("用户中断程序")
            finally:
                # 确保客户端正确断开连接
                if self.client.is_connected():
                    await self.client.disconnect()
                    self.logger.info("Telegram客户端已断开连接")

        self.client.loop.run_until_complete(_run_menu())

    async def interactive_menu(self) -> None:
        """交互式菜单"""
        while True:
            print("\n" + "="*60)
            print("🧹 TG频道/群组清理工具")
            print("="*60)
            print("1. 查看频道/群组信息")
            print("2. 获取消息统计")
            print("3. 按时间范围删除消息")
            print("4. 按关键词删除消息")
            print("5. 删除媒体消息")
            print("6. 删除所有消息（危险）")
            print("0. 退出")
            print("-"*60)
            
            choice = await self._async_input("请选择操作 (0-6): ")
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                await self.menu_chat_info()
            elif choice == '2':
                await self.menu_message_stats()
            elif choice == '3':
                await self.menu_delete_by_time()
            elif choice == '4':
                await self.menu_delete_by_keyword()
            elif choice == '5':
                await self.menu_delete_media()
            elif choice == '6':
                await self.menu_delete_all()
            else:
                print("❌ 无效选择，请重试")

    async def menu_chat_info(self) -> None:
        """菜单：查看频道信息"""
        chat_name = (await self._async_input("请输入频道/群组名称或ID: ")).strip()
        if not self._validate_chat_name(chat_name):
            print("❌ 频道/群组名称格式无效")
            return

        mode_choice = (await self._async_input("选择统计模式 - 快速模式(f)/精确模式(a) [默认:f]: ")).strip().lower()
        quick_mode = mode_choice != 'a'

        print("正在获取信息...")
        info = await self.get_chat_info(chat_name, quick_mode)

        if info:
            print(f"\n📊 频道/群组信息:")
            print(f"名称: {info['title']}")
            print(f"用户名: @{info['username']}" if info['username'] else "用户名: 无")
            print(f"类型: {info['type']}")
            print(f"ID: {info['id']}")
            print(f"成员数: {info['participants_count']}")
            print(f"消息数: {info['total_messages']}")
            print(f"创建时间: {info['created_date']}")
            if quick_mode:
                print("💡 提示: 使用了快速模式，消息数为估算值")

    async def menu_message_stats(self) -> None:
        """菜单：消息统计"""
        chat_name = (await self._async_input("请输入频道/群组名称或ID: ")).strip()
        if not chat_name:
            return

        limit = (await self._async_input("分析消息数量限制 (默认10000): ")).strip()
        limit = int(limit) if limit.isdigit() else 10000
        
        stats = await self.get_message_statistics(chat_name, limit)
        
        if stats:
            print(f"\n📈 消息统计:")
            print(f"总消息数: {stats['total_messages']}")
            print(f"文本消息: {stats['text_messages']}")
            print(f"媒体消息: {stats['media_messages']}")
            print(f"  - 图片: {stats['photo_messages']}")
            print(f"  - 视频: {stats['video_messages']}")
            print(f"  - 文档: {stats['document_messages']}")
            print(f"  - 贴纸: {stats['sticker_messages']}")
            print(f"  - 语音: {stats['voice_messages']}")
            print(f"活跃用户: {stats['unique_users']}")
            if stats['date_range']['earliest']:
                print(f"时间范围: {stats['date_range']['earliest']} 到 {stats['date_range']['latest']}")

    async def menu_delete_by_time(self) -> None:
        """菜单：按时间删除"""
        chat_name = (await self._async_input("请输入频道/群组名称或ID: ")).strip()
        if not self._validate_chat_name(chat_name):
            print("❌ 频道/群组名称格式无效")
            return

        start_time = (await self._async_input("开始时间 (格式: 2025-01-01 00:00): ")).strip()
        end_time = (await self._async_input("结束时间 (格式: 2025-01-01 23:59): ")).strip()

        if not self._validate_time_format(start_time):
            print("❌ 开始时间格式错误，请使用格式: YYYY-MM-DD HH:MM")
            return

        if not self._validate_time_format(end_time):
            print("❌ 结束时间格式错误，请使用格式: YYYY-MM-DD HH:MM")
            return

        # 验证时间逻辑
        try:
            start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M")
            end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M")
            if start_dt >= end_dt:
                print("❌ 开始时间必须早于结束时间")
                return
        except ValueError:
            print("❌ 时间解析失败")
            return

        print(f"⚠️  即将删除 {chat_name} 在 {start_time} 到 {end_time} 的所有消息")
        confirm = (await self._async_input("确认删除？(y/N): ")).strip().lower()

        if confirm == 'y':
            deleted = await self.delete_messages_in_time_range(chat_name, start_time, end_time)
            print(f"✅ 删除完成，共删除 {deleted} 条消息")

    async def menu_delete_by_keyword(self) -> None:
        """菜单：按关键词删除"""
        chat_name = (await self._async_input("请输入频道/群组名称或ID: ")).strip()
        if not self._validate_chat_name(chat_name):
            print("❌ 频道/群组名称格式无效")
            return

        keywords_str = (await self._async_input("请输入关键词 (多个关键词用逗号分隔): ")).strip()
        if not keywords_str:
            print("❌ 关键词不能为空")
            return

        keywords = [k.strip() for k in keywords_str.split(',')]
        if not self._validate_keywords(keywords):
            print("❌ 关键词格式无效，请确保每个关键词都不为空")
            return

        case_choice = (await self._async_input("是否区分大小写？(y/N): ")).strip().lower()
        case_sensitive = case_choice == 'y'

        print(f"⚠️  即将删除 {chat_name} 中包含关键词 {keywords} 的所有消息")
        print(f"   大小写敏感: {'是' if case_sensitive else '否'}")
        confirm = (await self._async_input("确认删除？(y/N): ")).strip().lower()

        if confirm == 'y':
            deleted = await self.delete_messages_by_keyword(chat_name, keywords, case_sensitive=case_sensitive)
            print(f"✅ 删除完成，共删除 {deleted} 条消息")

    async def menu_delete_media(self) -> None:
        """菜单：删除媒体消息"""
        chat_name = (await self._async_input("请输入频道/群组名称或ID: ")).strip()
        if not chat_name:
            return

        print("选择要删除的媒体类型:")
        print("1. 图片 (photo)")
        print("2. 视频 (video)")
        print("3. 文档 (document)")
        print("4. 贴纸 (sticker)")
        print("5. 语音 (voice)")
        print("6. 所有媒体")

        choice = (await self._async_input("请选择 (1-6): ")).strip()
        
        media_map = {
            '1': ['photo'],
            '2': ['video'],
            '3': ['document'],
            '4': ['sticker'],
            '5': ['voice'],
            '6': ['photo', 'video', 'document', 'sticker', 'voice']
        }
        
        if choice not in media_map:
            print("❌ 无效选择")
            return
        
        media_types = media_map[choice]
        
        print(f"⚠️  即将删除 {chat_name} 中的 {media_types} 类型消息")
        confirm = (await self._async_input("确认删除？(y/N): ")).strip().lower()
        
        if confirm == 'y':
            deleted = await self.delete_media_messages(chat_name, media_types)
            print(f"✅ 删除完成，共删除 {deleted} 条消息")

    async def menu_delete_all(self) -> None:
        """菜单：删除所有消息"""
        chat_name = (await self._async_input("请输入频道/群组名称或ID: ")).strip()
        if not chat_name:
            return
        
        print("⚠️  危险操作：即将删除所有消息！")
        print("这个操作不可恢复！")
        
        deleted = await self.delete_all_messages(chat_name)
        if deleted > 0:
            print(f"✅ 删除完成，共删除 {deleted} 条消息")


# ==================== 程序入口 ====================
if __name__ == '__main__':
    # 验证配置 (API_ID已在load_api_config中验证)
    if not API_HASH or API_HASH == 'your_api_hash':
        print("❌ 错误: 请在.env文件中设置 TG_API_HASH")
        exit(1)

    if not STRING_SESSION or STRING_SESSION == 'your_string_session':
        print("❌ 错误: 请在.env文件中设置 TG_STRING_SESSION")
        exit(1)

    # 配置全局日志
    log_level = getattr(logging, LOG_LEVEL, logging.INFO)
    # 获取脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    log_file_path = os.path.join(script_dir, 'tg_cleaner.log')

    # 避免重复配置日志
    if not logging.getLogger().handlers:
        logging.basicConfig(
            level=log_level,
            format='[%(levelname)s %(asctime)s] %(name)s: %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler(log_file_path, encoding='utf-8')
            ]
        )

    print(f"✅ 配置加载成功")
    print(f"   API ID: {API_ID}")
    print(f"   代理: {'已配置' if PROXY else '未配置'}")
    print(f"   日志级别: {LOG_LEVEL}")

    # 创建清理工具实例
    cleaner = TGCleaner(
        api_id=API_ID,
        api_hash=API_HASH,
        string_session=STRING_SESSION,
        proxy=PROXY
    )

    # 运行交互式菜单
    cleaner.run_with_menu()
