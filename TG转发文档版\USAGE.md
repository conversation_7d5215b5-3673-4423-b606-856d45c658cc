# 使用指南

## 快速开始

### 1. 安装依赖
```bash
python install.py
```

### 2. 配置环境
```bash
cp .env.example .env
```

### 3. 编辑配置文件
编辑 `.env` 文件，填入以下必要信息：

```env
# Telegram API 配置（从 https://my.telegram.org 获取）
TG_API_ID=12345678
TG_API_HASH=abcdef1234567890abcdef1234567890
TG_STRING_SESSION=你的会话字符串

# 目标频道
TARGET_CHANNEL=@your_channel_username
```

### 4. 运行程序
```bash
python main.py
```

## 配置说明

| 配置项 | 必填 | 说明 |
|--------|------|------|
| `TG_API_ID` | ✅ | Telegram API ID |
| `TG_API_HASH` | ✅ | Telegram API Hash |
| `TG_STRING_SESSION` | ✅ | Telegram 会话字符串 |
| `TARGET_CHANNEL` | ✅ | 目标频道用户名或ID |
| `KDOCS_URL` | ❌ | 金山文档链接（默认已配置） |
| `HEADLESS` | ❌ | 无头模式（默认 true） |
| `DELAY_BETWEEN_REQUESTS` | ❌ | 请求间隔秒数（默认 2） |
| `MAX_RETRIES` | ❌ | 最大重试次数（默认 3） |

## 常见问题

### Q: 如何获取 Telegram API 配置？
A: 访问 https://my.telegram.org，登录后创建应用获取 API ID 和 Hash。

### Q: 如何获取 String Session？
A: 使用其他 Telegram 工具生成会话字符串，然后填入配置文件。

### Q: 频道 ID 格式是什么？
A: 
- 公开频道：`@channel_username`
- 私有频道：`-1001234567890`（负数ID）

### Q: 程序运行失败怎么办？
A: 
1. 检查 `.env` 配置是否正确
2. 确认有频道发送权限
3. 查看 `app.log` 日志文件
4. 检查网络连接

## 运行方式

```bash
# 方式一：直接运行
cd TG转发文档版
python main.py

# 方式二：使用启动脚本
python TG转发文档版/run.py

# 方式三：从任意目录
python TG转发文档版/main.py
```
