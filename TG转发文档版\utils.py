import re
import logging
from typing import List, Dict, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class ContentProcessor:
    """内容处理工具类"""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff\[\]()（）【】.,，。！!？?：:\-_]', '', text)
        
        return text
    
    @staticmethod
    def extract_year(text: str) -> Optional[int]:
        """提取年份"""
        year_match = re.search(r'\[?(20\d{2})\]?', text)
        if year_match:
            return int(year_match.group(1))
        return None
    
    @staticmethod
    def extract_quality(text: str) -> Optional[str]:
        """提取画质信息"""
        quality_patterns = [
            r'4K',
            r'高码',
            r'1080[pP]',
            r'720[pP]',
            r'超清',
            r'高清'
        ]
        
        for pattern in quality_patterns:
            if re.search(pattern, text, re.IGNORECASE):
                return re.search(pattern, text, re.IGNORECASE).group()
        
        return None
    
    @staticmethod
    def extract_episode_info(text: str) -> Dict[str, Optional[str]]:
        """提取集数信息"""
        info = {
            'total_episodes': None,
            'current_episode': None,
            'season': None
        }
        
        # 总集数
        total_match = re.search(r'全(\d+)集', text)
        if total_match:
            info['total_episodes'] = total_match.group(1)
        
        # 当前集数
        current_match = re.search(r'更新至(\d+)', text)
        if current_match:
            info['current_episode'] = current_match.group(1)
        
        # 季数
        season_match = re.search(r'第([一二三四五六七八九十\d]+)季', text)
        if season_match:
            info['season'] = season_match.group(1)
        
        return info

class LinkExtractor:
    """链接提取工具"""
    
    @staticmethod
    def extract_pan_links(text: str) -> List[Dict[str, str]]:
        """提取网盘链接"""
        links = []
        
        # 夸克网盘
        quark_pattern = r'https?://pan\.quark\.cn/s/[a-zA-Z0-9]+'
        quark_matches = re.findall(quark_pattern, text)
        for link in quark_matches:
            links.append({
                'url': link,
                'type': 'quark',
                'name': '夸克网盘'
            })
        
        # 迅雷网盘
        xunlei_pattern = r'https?://pan\.xunlei\.com/s/[a-zA-Z0-9\-_]+(?:\?pwd=[a-zA-Z0-9]+)?#?'
        xunlei_matches = re.findall(xunlei_pattern, text)
        for link in xunlei_matches:
            links.append({
                'url': link,
                'type': 'xunlei',
                'name': '迅雷网盘'
            })
        
        return links
    
    @staticmethod
    def extract_password(text: str) -> Optional[str]:
        """提取提取码"""
        pwd_patterns = [
            r'pwd=([a-zA-Z0-9]+)',
            r'提取码[：:]?\s*([a-zA-Z0-9]+)',
            r'密码[：:]?\s*([a-zA-Z0-9]+)'
        ]
        
        for pattern in pwd_patterns:
            match = re.search(pattern, text)
            if match:
                return match.group(1)
        
        return None

class MessageFormatter:
    """消息格式化工具"""
    
    @staticmethod
    def format_movie_message(item: Dict) -> str:
        """格式化电影消息 - 简洁格式"""
        text = item['text']
        links = item.get('links', [])

        # 加粗标题
        message = f"**{text}**"

        # 添加链接 - 每行一个链接
        if links:
            for link in links:
                url = link.get('url', '')
                if url:
                    message += f"\n{url}"

        return message
    
    @staticmethod
    def format_tv_series_message(item: Dict) -> str:
        """格式化电视剧消息 - 简洁格式"""
        text = item['text']
        links = item.get('links', [])

        # 加粗标题
        message = f"**{text}**"

        # 添加链接 - 每行一个链接
        if links:
            for link in links:
                url = link.get('url', '')
                if url:
                    message += f"\n{url}"

        return message
    
    @staticmethod
    def format_general_message(item: Dict) -> str:
        """格式化通用消息 - 简洁格式"""
        text = item['text']
        links = item.get('links', [])

        # 加粗标题
        message = f"**{text}**"

        # 添加链接 - 每行一个链接
        if links:
            for link in links:
                url = link.get('url', '')
                if url:
                    message += f"\n{url}"

        return message

class DateTimeHelper:
    """日期时间工具"""
    
    @staticmethod
    def get_current_timestamp() -> str:
        """获取当前时间戳"""
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    def parse_date_from_text(text: str) -> Optional[datetime]:
        """从文本中解析日期"""
        date_pattern = r'(\d{4})\.(\d{2})\.(\d{2})'
        match = re.search(date_pattern, text)
        
        if match:
            year, month, day = match.groups()
            try:
                return datetime(int(year), int(month), int(day))
            except ValueError:
                return None
        
        return None
