# TG 转发文档版

自动爬取金山文档内容并转发到 Telegram 频道的工具。

## 功能特点

- 🚀 使用 Playwright 无头浏览器爬取动态内容
- 🎯 智能内容分类（电影、电视剧、短剧等）
- 📱 使用 Telethon 完整 Telegram API 功能
- 🔄 自动去重和格式化
- ⚡ 异步处理，支持批量操作
- 🛡️ 错误重试和频率限制处理

## 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
playwright install chromium
```

### 2. 配置环境
```bash
cp .env.example .env
# 编辑 .env 文件，填入你的配置
```

### 3. 配置参数

编辑 `.env` 文件，填入你的配置：

```env
# Telegram API 配置
TG_API_ID=your_api_id
TG_API_HASH=your_api_hash
TG_STRING_SESSION=your_string_session

# 目标频道
TARGET_CHANNEL=@your_channel_username

# 金山文档链接
KDOCS_URL=https://www.kdocs.cn/l/caRody46qOEl
```

### 4. 验证配置

```bash
# 验证配置是否正确
python validate_config.py
```

### 5. 测试爬取功能

```bash
# 测试文档爬取和内容整理
python test_scraper.py

# 详细调试信息
python debug_scraper.py
```

### 6. 运行程序

```bash
# 方法一：直接运行（推荐）
cd TG转发文档版
python main.py

# 方法二：使用启动脚本（任意目录）
python TG转发文档版/run.py

# 方法三：从任意目录运行
python TG转发文档版/main.py
```

## 项目结构

```
TG转发文档版/
├── main.py              # 主程序入口
├── run.py               # 启动脚本（支持任意目录运行）
├── config.py            # 配置管理
├── scraper.py           # 文档爬取模块
├── telegram_client.py   # Telegram 客户端
├── utils.py             # 工具函数集合
├── install.py           # 自动安装脚本
├── test_scraper.py      # 爬取功能测试
├── debug_scraper.py     # 详细调试工具
├── validate_config.py   # 配置验证工具
├── requirements.txt     # 依赖包
├── .env.example        # 配置模板
├── .gitignore          # Git 忽略文件
└── README.md           # 说明文档
```

## 配置说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `HEADLESS` | 无头模式运行 | `true` |
| `DELAY_BETWEEN_REQUESTS` | 请求间隔(秒) | `2` |
| `MAX_RETRIES` | 最大重试次数 | `3` |
| `ENABLE_CLASSIFICATION` | 启用内容分类 | `true` |
| `MIN_CONTENT_LENGTH` | 最小内容长度 | `10` |

## 功能特性

### 🔍 智能内容提取
- 多种选择器策略，适应不同文档结构
- 智能噪音过滤，去除无用内容
- 自动去重，避免重复内容

### 🏷️ 内容分类
- 自动识别分类标题（如：2025.05.22 周四更新的）
- 智能内容分类：电影、电视剧、短剧、动漫、综艺、软件等
- 可配置的分类规则

### 🔗 链接提取
- 支持多种网盘：夸克、迅雷、百度、阿里云盘、蓝奏云
- 智能链接识别和分类
- 自动去重相同链接

### 📱 消息格式化
- 美观的 Markdown 格式
- 分类标题和内容清晰分离
- 网盘链接预览卡片

### 🛠️ 调试工具
- 配置验证工具
- 爬取功能测试
- 详细调试信息输出

## 注意事项

- 确保有目标频道的发送权限
- 必须配置正确的 `TG_STRING_SESSION` 到 `.env` 文件
- 建议在服务器上使用无头模式
- 注意 Telegram API 的频率限制
- String Session 相当于登录凭据，请妥善保管
