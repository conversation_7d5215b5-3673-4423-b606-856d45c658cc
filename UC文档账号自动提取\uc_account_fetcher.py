#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UC账号密码获取脚本
定时从语雀页面获取UC浏览器VIP账号密码信息
"""

import asyncio
import json
import re
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uc_fetcher.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UCAccountFetcher:
    def __init__(self, output_file='uc_account.json'):
        self.url = "https://www.yuque.com/ganjuezijitaihaole/ovcqrm/ni0t2qna08dnw2q7?singleDoc#"
        self.output_file = Path(output_file)

    async def fetch_account_info(self):
        """获取UC账号信息"""
        try:
            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()

                # 设置用户代理
                await page.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })

                logger.info(f"正在访问页面: {self.url}")
                await page.goto(self.url, wait_until='networkidle')

                # 等待页面内容加载
                await page.wait_for_timeout(5000)

                # 获取页面内容
                content = await page.content()

                # 提取账号信息
                account_info = await self._extract_account_info(page, content)

                await browser.close()
                return account_info

        except Exception as e:
            logger.error(f"获取账号信息失败: {str(e)}")
            return None
    
    async def _extract_account_info(self, page, content):
        """从页面内容中提取账号信息"""
        try:
            # 获取页面纯文本内容
            text_content = await page.inner_text('body')

            logger.info(f"页面内容长度: {len(content)}")
            logger.info(f"纯文本内容长度: {len(text_content)}")

            # 调试：保存内容到文件
            with open('debug_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            with open('debug_text.txt', 'w', encoding='utf-8') as f:
                f.write(text_content)

            # 基于实际页面内容的精确匹配
            account = None
            password = None

            # 方法1: 在纯文本中查找特定的账号密码模式
            # 根据调试文件，账号密码在独立的行中
            lines = text_content.split('\n')

            for i, line in enumerate(lines):
                line = line.strip()

                # 查找12位数字账号（************）
                if re.match(r'^\s*\d{12}\s*$', line):
                    account = re.sub(r'\s+', '', line)
                    logger.info(f"在第{i+1}行找到账号: {account}")

                # 查找uc开头的密码（uc112233）
                if re.match(r'^\s*uc\d+\s*$', line, re.IGNORECASE):
                    password = re.sub(r'\s+', '', line)
                    logger.info(f"在第{i+1}行找到密码: {password}")

                # 如果都找到了就退出
                if account and password:
                    break

            # 方法2: 如果上面没找到，使用更宽松的匹配
            if not account or not password:
                # 查找账号：在"新账号"附近的12位数字
                account_pattern = r'新账号[^\d]*(\d{12})'
                account_match = re.search(account_pattern, text_content, re.IGNORECASE)
                if account_match and not account:
                    account = account_match.group(1)
                    logger.info(f"通过上下文找到账号: {account}")

                # 查找密码：在账号附近的uc开头字符串
                if account:
                    # 在账号附近查找密码
                    account_pos = text_content.find(account)
                    if account_pos != -1:
                        # 在账号前后200个字符内查找密码
                        context = text_content[max(0, account_pos-200):account_pos+200]
                        password_match = re.search(r'uc\d+', context, re.IGNORECASE)
                        if password_match and not password:
                            password = password_match.group(0)
                            logger.info(f"在账号附近找到密码: {password}")

            # 方法3: 最后的备用匹配
            if not account or not password:
                # 直接在整个文本中查找，但排除明显错误的结果
                if not account:
                    # 查找12位数字，但排除时间戳等
                    account_matches = re.findall(r'\b(\d{12})\b', text_content)
                    for match in account_matches:
                        # 排除明显的时间戳（以17、20开头的）
                        if not match.startswith(('17', '20')):
                            account = match
                            logger.info(f"备用方法找到账号: {account}")
                            break

                if not password:
                    password_matches = re.findall(r'\buc\d+\b', text_content, re.IGNORECASE)
                    if password_matches:
                        # 选择最长的密码（通常是正确的）
                        password = max(password_matches, key=len)
                        logger.info(f"备用方法找到密码: {password}")

            # 获取更新时间
            update_time = await self._extract_update_time(page, content)

            if account and password:
                account_info = {
                    'account': account,
                    'password': password,
                    'update_time': update_time,
                    'fetch_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'source_url': self.url
                }

                logger.info(f"成功获取账号信息: {account} / {password}")
                return account_info
            else:
                logger.warning("未能提取到完整的账号密码信息")
                logger.warning(f"账号: {account}, 密码: {password}")
                return None

        except Exception as e:
            logger.error(f"提取账号信息时出错: {str(e)}")
            return None
    
    async def _extract_update_time(self, page, content):
        """提取更新时间"""
        try:
            # 尝试多种时间格式匹配
            time_patterns = [
                r'今天\s+(\d{1,2}:\d{2})',
                r'昨天\s+(\d{1,2}:\d{2})',
                r'(\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2})',
                r'(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2})'
            ]

            # 从纯文本内容中提取时间
            text_content = await page.inner_text('body')

            for pattern in time_patterns:
                match = re.search(pattern, text_content)
                if match:
                    return match.group(0)

            return "未知时间"

        except Exception as e:
            logger.warning(f"提取更新时间失败: {str(e)}")
            return "提取失败"
    
    def save_to_file(self, account_info):
        """保存账号信息到文件"""
        try:
            if account_info:
                # 保存为JSON格式
                with open(self.output_file, 'w', encoding='utf-8') as f:
                    json.dump(account_info, f, ensure_ascii=False, indent=2)
                
                # 同时保存为纯文本格式便于查看
                txt_file = self.output_file.with_suffix('.txt')
                with open(txt_file, 'w', encoding='utf-8') as f:
                    f.write(f"UC浏览器VIP账号信息\n")
                    f.write(f"=" * 30 + "\n")
                    f.write(f"账号: {account_info['account']}\n")
                    f.write(f"密码: {account_info['password']}\n")
                    f.write(f"页面更新时间: {account_info['update_time']}\n")
                    f.write(f"获取时间: {account_info['fetch_time']}\n")
                    f.write(f"来源: {account_info['source_url']}\n")
                
                logger.info(f"账号信息已保存到: {self.output_file} 和 {txt_file}")
                return True
            else:
                logger.warning("没有有效的账号信息可保存")
                return False
                
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            return False
    
    async def run(self):
        """运行获取流程"""
        logger.info("开始获取UC账号信息...")

        account_info = await self.fetch_account_info()

        if account_info:
            success = self.save_to_file(account_info)
            if success:
                logger.info("任务完成!")
                return account_info
            else:
                logger.error("保存失败!")
                return None
        else:
            logger.error("获取账号信息失败!")
            return None

async def main():
    """主函数"""
    fetcher = UCAccountFetcher()
    result = await fetcher.run()

    if result:
        print(f"\n获取成功:")
        print(f"账号: {result['account']}")
        print(f"密码: {result['password']}")
        print(f"更新时间: {result['update_time']}")
    else:
        print("获取失败，请查看日志文件")

if __name__ == "__main__":
    asyncio.run(main())
