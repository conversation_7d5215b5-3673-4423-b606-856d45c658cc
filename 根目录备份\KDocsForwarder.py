import os
import requests
import random
import time
import json
import re
import asyncio
from datetime import datetime, timezone, timedelta
from telethon import TelegramClient, functions
from telethon.sessions import StringSession
from bs4 import BeautifulSoup

class KDocsForwarder:
    def __init__(self, api_id, api_hash, string_session, kdocs_urls, forward_to_channel,
                 include, exclude, proxy, checknum, replacements, message_md, channel_match, 
                 hyperlink_text, past_years, only_today, check_interval):
        
        # 支持的网盘关键词
        self.urls_kw = ['ed2k','magnet', 'drive.uc.cn', 'caiyun.139.com', 'cloud.189.cn', 
                       'pan.quark.cn', '115cdn.com','115.com', 'anxia.com', 'alipan.com', 
                       'aliyundrive.com','pan.baidu.com','mypikpak.com','123684.com',
                       '123685.com','123912.com','123pan.com','123pan.cn','123592.com',
                       'pan.xunlei.com']
        
        # URL匹配模式
        self.url_patterns = [
            r'https://pan\.quark\.cn/s/[a-zA-Z0-9]+',
            r'https://pan\.xunlei\.com/s/[a-zA-Z0-9_-]+\?pwd=[a-zA-Z0-9]+#?',
            r'https://pan\.baidu\.com/s/[a-zA-Z0-9_-]+',
            r'https://alipan\.com/s/[a-zA-Z0-9]+',
            r'https://aliyundrive\.com/s/[a-zA-Z0-9]+',
            r'magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s]*',
            r'ed2k://\|file\|[^|]+\|\d+\|[A-Fa-f0-9]+\|/?'
        ]
        
        self.checkbox = {"links":[],"sizes":[],"bot_links":{},"chat_forward_count_msg_id":{},"today":"","today_count":0}
        self.checknum = checknum
        self.today_count = 0
        self.history = 'history.json'
        
        # 配置参数
        self.api_id = api_id
        self.api_hash = api_hash
        self.string_session = string_session
        self.kdocs_urls = kdocs_urls  # 金山文档URL列表
        self.forward_to_channel = forward_to_channel
        self.include = include
        
        # 时区设置
        self.china_timezone_offset = timedelta(hours=8)
        self.today = (datetime.utcnow() + self.china_timezone_offset).date()
        
        # 年份过滤
        current_year = datetime.now().year - 2
        if not past_years:
            years_list = [str(year) for year in range(1895, current_year)]
            self.exclude = exclude + years_list
        else:
            self.exclude = exclude
            
        self.only_today = only_today
        self.hyperlink_text = hyperlink_text
        self.replacements = replacements
        self.message_md = message_md
        self.channel_match = channel_match
        self.check_interval = check_interval  # 检查间隔（秒）
        
        # TG客户端
        self.client = TelegramClient(StringSession(string_session), api_id, api_hash, proxy=proxy)
        
        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def random_wait(self, min_ms, max_ms):
        """随机等待"""
        min_sec = min_ms / 1000
        max_sec = max_ms / 1000
        wait_time = random.uniform(min_sec, max_sec)
        time.sleep(wait_time)

    def contains(self, s, include):
        """检查是否包含关键词"""
        return any(k in s for k in include)

    def nocontains(self, s, exclude):
        """检查是否不包含排除词"""
        return not any(k in s for k in exclude)

    def replace_targets(self, message: str):
        """根据用户自定义的替换规则替换文本内容"""
        if self.replacements:
            for target_word, source_words in self.replacements.items():
                if isinstance(source_words, str):
                    source_words = [source_words]
                for word in source_words:
                    message = message.replace(word, target_word)
        return message.strip()

    def identify_platform(self, url):
        """识别网盘平台"""
        platform_map = {
            'pan.quark.cn': '夸克',
            'pan.xunlei.com': '迅雷',
            'pan.baidu.com': '百度',
            'alipan.com': '阿里云',
            'aliyundrive.com': '阿里云',
            '115.com': '115',
            'cloud.189.cn': '天翼',
            'caiyun.139.com': '移动',
            'mypikpak.com': 'PikPak',
            '123pan.com': '123盘',
            'magnet:': '磁力',
            'ed2k:': 'ed2k'
        }
        
        for key, value in platform_map.items():
            if key in url:
                return value
        return '其他'

    def parse_kdocs_content(self, content):
        """解析金山文档内容，提取标题和链接"""
        results = []
        lines = content.strip().split('\n')

        # 预处理：找到所有可能的标题行
        title_candidates = []
        link_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 检查是否包含链接
            has_link = any(re.search(pattern, line) for pattern in self.url_patterns)

            if has_link:
                link_lines.append((i, line))
            elif self.is_likely_title(line):
                title_candidates.append((i, line))

        # 为每个链接行找到最近的标题
        for link_line_idx, link_line in link_lines:
            # 提取链接
            links_found = []
            for pattern in self.url_patterns:
                matches = re.findall(pattern, link_line)
                for match in matches:
                    platform = self.identify_platform(match)
                    links_found.append({
                        'url': match,
                        'platform': platform
                    })

            # 找到最近的标题（向前查找）
            best_title = "Unknown"
            min_distance = float('inf')

            for title_idx, title_line in title_candidates:
                if title_idx < link_line_idx:  # 标题应该在链接之前
                    distance = link_line_idx - title_idx
                    if distance < min_distance:
                        min_distance = distance
                        best_title = title_line

            # 如果没找到合适的标题，尝试从附近的行中提取
            if best_title == "Unknown" or min_distance > 10:
                best_title = self.extract_title_from_nearby_lines(lines, link_line_idx)

            # 为每个链接创建结果
            for link in links_found:
                results.append({
                    'title': best_title,
                    'url': link['url'],
                    'platform': link['platform'],
                    'message': f"**{best_title}**\n\n{link['platform']}: {link['url']}"
                })

        return results

    def is_likely_title(self, line):
        """判断一行是否可能是标题"""
        # 过滤掉明显不是标题的行
        if len(line) < 5 or len(line) > 200:
            return False

        # 排除包含特定词汇的行
        exclude_words = ['搜索方法', '注意', '红包', '优惠', '登录', '点击', '输入', '获取']
        if any(word in line for word in exclude_words):
            return False

        # 包含影视相关关键词的更可能是标题
        title_keywords = ['4K', '高清', '更新至', '第', '季', '集', '电影', '电视剧', '动漫', '高码', '蓝光', '超前']
        if any(keyword in line for keyword in title_keywords):
            return True

        # 包含年份的可能是标题
        if re.search(r'\[?\d{4}\]?', line):
            return True

        # 包含中文且长度适中的可能是标题
        if re.search(r'[\u4e00-\u9fff]', line) and 5 <= len(line) <= 100:
            return True

        return False

    def extract_title_from_nearby_lines(self, lines, current_index):
        """从附近的行中提取标题"""
        # 向前查找标题（最多查找10行）
        for i in range(max(0, current_index - 10), current_index):
            line = lines[i].strip()
            if self.is_likely_title(line):
                return line

        # 如果向前没找到，向后查找（最多查找3行）
        for i in range(current_index + 1, min(len(lines), current_index + 4)):
            line = lines[i].strip()
            if self.is_likely_title(line):
                return line

        return "Unknown"

    def fetch_kdocs_content(self, url):
        """获取金山文档内容"""
        try:
            self.random_wait(1000, 3000)
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            # 保存原始HTML用于调试
            with open('debug_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)

            soup = BeautifulSoup(response.text, 'html.parser')

            # 尝试多种方式提取内容
            content_methods = [
                # 方法1: 查找特定的内容容器
                lambda: self.extract_from_containers(soup),
                # 方法2: 查找所有链接及其上下文
                lambda: self.extract_from_links(soup),
                # 方法3: 从页面文本中提取
                lambda: soup.get_text()
            ]

            for i, method in enumerate(content_methods, 1):
                try:
                    content = method()
                    if content and len(content.strip()) > 100:  # 确保内容不为空
                        print(f"使用方法 {i} 成功提取内容，长度: {len(content)}")
                        return content
                except Exception as e:
                    print(f"方法 {i} 失败: {e}")
                    continue

            print("所有提取方法都失败了")
            return None

        except Exception as e:
            print(f"获取文档内容失败 {url}: {e}")
            return None

    def extract_from_containers(self, soup):
        """从特定容器中提取内容"""
        # 常见的内容容器选择器
        selectors = [
            'div[class*="content"]',
            'div[class*="document"]',
            'div[class*="editor"]',
            'div[class*="text"]',
            'main',
            'article',
            '.content',
            '.document-content',
            '.editor-content'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                content = '\n'.join([elem.get_text() for elem in elements])
                if len(content.strip()) > 100:
                    return content

        return None

    def extract_from_links(self, soup):
        """从链接及其上下文中提取内容"""
        content_parts = []

        # 查找所有包含网盘链接的元素
        for pattern in self.url_patterns:
            # 在HTML中查找匹配的链接
            import re
            matches = re.finditer(pattern, str(soup))
            for match in matches:
                link = match.group(0)
                # 尝试找到包含这个链接的元素
                link_elements = soup.find_all('a', href=re.compile(re.escape(link)))
                for elem in link_elements:
                    # 获取父元素的文本作为上下文
                    parent = elem.parent
                    if parent:
                        context = parent.get_text().strip()
                        if context:
                            content_parts.append(context)

        return '\n'.join(content_parts) if content_parts else None

    def categorize_urls(self, urls):
        """将 URL 按云盘厂商分类"""
        categories = {
            "magnet": ["magnet"],
            "ed2k": ["ed2k"],
            "uc": ["drive.uc.cn"],
            "mobile": ["caiyun.139.com"],
            "tianyi": ["cloud.189.cn"],
            "quark": ["pan.quark.cn"],
            "115": ["115cdn.com","115.com", "anxia.com"],
            "aliyun": ["alipan.com", "aliyundrive.com"],
            "pikpak": ["mypikpak.com"],
            "baidu": ["pan.baidu.com"],
            "xunlei": ["pan.xunlei.com"],
            "123": ['123684.com','123685.com','123912.com','123pan.com','123pan.cn','123592.com'],
            "others": []
        }
        
        result = {category: [] for category in categories}
        
        for url_info in urls:
            url = url_info['url'] if isinstance(url_info, dict) else url_info
            categorized = False
            
            for category, domains in categories.items():
                if any(domain in url for domain in domains):
                    result[category].append(url_info if isinstance(url_info, dict) else {'url': url})
                    categorized = True
                    break
                    
            if not categorized:
                result["others"].append(url_info if isinstance(url_info, dict) else {'url': url})
                
        return result

    async def checkhistory(self):
        """检索历史消息用于过滤去重"""
        links = []
        sizes = []
        if os.path.exists(self.history):
            with open(self.history, 'r', encoding='utf-8') as f:
                self.checkbox = json.loads(f.read())
                if self.checkbox.get('today') == datetime.now().strftime("%Y-%m-%d"):
                    links = self.checkbox['links']
                    sizes = self.checkbox['sizes']
                else:
                    self.checkbox['links'] = []
                    self.checkbox['sizes'] = []
                    self.checkbox["bot_links"] = {}
                    self.checkbox["today_count"] = 0
                self.today_count = self.checkbox.get('today_count') if self.checkbox.get('today_count') else self.checknum

        self.checknum = self.checknum if self.today_count < self.checknum else self.today_count

        # 从目标频道获取历史链接
        try:
            chat = await self.client.get_entity(self.forward_to_channel)
            messages = self.client.iter_messages(chat, limit=self.checknum)
            async for message in messages:
                if message.message:
                    # 提取消息中的链接
                    for pattern in self.url_patterns:
                        matches = re.findall(pattern, message.message)
                        if matches:
                            links.extend(matches)
        except Exception as e:
            print(f"获取历史消息失败: {e}")

        return list(set(links)), list(set(sizes))

    async def send(self, message_text, target_chat_name):
        """发送消息到TG频道"""
        try:
            if self.nocontains(message_text, self.urls_kw):
                return

            processed_text = self.replace_targets(message_text)
            await self.client.send_message(target_chat_name, processed_text, parse_mode='md')
            print(f"发送成功: {processed_text[:50]}...")

        except Exception as e:
            print(f'发送消息失败: {e}')

    async def dispatch_channel(self, resource_info):
        """根据规则分发到不同频道"""
        message_text = resource_info['message']

        hit = False
        if self.channel_match:
            for rule in self.channel_match:
                if rule.get('include'):
                    if not self.contains(message_text, rule['include']):
                        continue
                if rule.get('exclude'):
                    if not self.nocontains(message_text, rule['exclude']):
                        continue
                await self.send(message_text, rule['target'])
                hit = True
            if not hit:
                await self.send(message_text, self.forward_to_channel)
        else:
            await self.send(message_text, self.forward_to_channel)

    async def process_kdocs_resources(self, links, sizes):
        """处理金山文档中的资源"""
        total = 0
        new_links = []

        for kdocs_url in self.kdocs_urls:
            print(f'正在处理文档: {kdocs_url}')

            # 获取文档内容
            content = self.fetch_kdocs_content(kdocs_url)
            if not content:
                continue

            # 解析资源
            resources = self.parse_kdocs_content(content)
            print(f'从文档中提取到 {len(resources)} 个资源')

            for resource in resources:
                # 检查是否符合包含条件
                if not self.contains(resource['title'], self.include):
                    continue

                # 检查是否包含排除词
                if not self.nocontains(resource['title'], self.exclude):
                    continue

                # 检查链接是否已存在
                if resource['url'] in links:
                    print(f'链接已存在: {resource["url"]}')
                    continue

                # 发送到频道
                await self.dispatch_channel(resource)

                # 记录新链接
                new_links.append(resource['url'])
                total += 1

                # 随机等待
                self.random_wait(200, 1000)

        print(f"本次共转发 {total} 个新资源")
        return links + new_links, sizes

    async def daily_forwarded_count(self, target_channel):
        """统计今日更新"""
        china_offset = timedelta(hours=8)
        china_tz = timezone(china_offset)
        now = datetime.now(china_tz)
        start_of_day_china = datetime.combine(now.date(), datetime.min.time())
        start_of_day_china = start_of_day_china.replace(tzinfo=china_tz)
        start_of_day_utc = start_of_day_china.astimezone(timezone.utc)

        try:
            from telethon.tl.functions.messages import GetHistoryRequest
            result = await self.client(GetHistoryRequest(
                peer=target_channel,
                limit=1,
                offset_date=start_of_day_utc,
                offset_id=0,
                add_offset=0,
                max_id=0,
                min_id=0,
                hash=0
            ))

            first_message_pos = result.offset_id_offset
            today_count = first_message_pos if first_message_pos else 0
            msg = f'**今日共更新【{today_count}】条资源**\n\n'
            msg = msg + self.message_md
            return msg, today_count
        except Exception as e:
            print(f"统计今日更新失败: {e}")
            return f'**今日更新统计**\n\n{self.message_md}', 0

    async def send_daily_forwarded_count(self):
        """发送每日统计"""
        try:
            msg, tc = await self.daily_forwarded_count(self.forward_to_channel)
            sent_message = await self.client.send_message(self.forward_to_channel, msg, parse_mode='md', link_preview=False)
            self.checkbox["today_count"] = tc

            # 置顶消息
            await self.client.pin_message(self.forward_to_channel, sent_message.id)

            chat_forward_count_msg_id = {self.forward_to_channel: sent_message.id}

            if self.channel_match:
                for rule in self.channel_match:
                    m, t = await self.daily_forwarded_count(rule['target'])
                    sm = await self.client.send_message(rule['target'], m, parse_mode='md')
                    self.checkbox["today_count"] = self.checkbox["today_count"] + t
                    chat_forward_count_msg_id[rule['target']] = sm.id
                    await self.client.pin_message(rule['target'], sm.id)

            self.checkbox["chat_forward_count_msg_id"] = chat_forward_count_msg_id
        except Exception as e:
            print(f"发送统计失败: {e}")

    async def main(self):
        """主要处理流程"""
        start_time = time.time()

        # 检查历史记录
        links, sizes = await self.checkhistory()
        print(f'历史链接数: {len(links)}')

        # 处理金山文档资源
        links, sizes = await self.process_kdocs_resources(links, sizes)

        # 发送统计
        await self.send_daily_forwarded_count()

        # 保存历史记录
        with open(self.history, 'w+', encoding='utf-8') as f:
            self.checkbox['links'] = list(set(links))[-self.checkbox["today_count"]:]
            self.checkbox['sizes'] = list(set(sizes))[-self.checkbox["today_count"]:]
            self.checkbox['today'] = datetime.now().strftime("%Y-%m-%d")
            f.write(json.dumps(self.checkbox, ensure_ascii=False, indent=2))

        await self.client.disconnect()
        end_time = time.time()
        print(f'处理完成，耗时: {end_time - start_time:.2f} 秒')

    def run(self):
        """运行转发器"""
        with self.client.start():
            self.client.loop.run_until_complete(self.main())

    async def run_continuous(self):
        """持续运行模式"""
        print(f"开始持续监控模式，检查间隔: {self.check_interval} 秒")

        while True:
            try:
                print(f"\n=== {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} 开始检查 ===")
                await self.main()
                print(f"等待 {self.check_interval} 秒后进行下次检查...")
                await asyncio.sleep(self.check_interval)
            except KeyboardInterrupt:
                print("收到停止信号，退出程序")
                break
            except Exception as e:
                print(f"运行出错: {e}")
                print(f"等待 {self.check_interval} 秒后重试...")
                await asyncio.sleep(self.check_interval)

    def run_continuous_mode(self):
        """运行持续监控模式"""
        with self.client.start():
            self.client.loop.run_until_complete(self.run_continuous())
