# 影视资源管理系统

一个完整的影视资源自动化管理系统，支持从金山文档获取、提取、过滤和转发资源到Telegram频道。

## 🏗️ 系统架构

### 核心基础设施模块

#### `config.py` - 统一配置管理
- 集中管理所有配置项，支持环境变量覆盖
- 提供类型安全的配置访问接口
- 支持文件路径、过滤关键词、API配置等各类设置

#### `logger.py` - 统一日志系统
- 标准化日志格式和级别管理
- 支持控制台和文件双重输出
- 提供便捷的日志器获取接口

#### `exceptions.py` - 异常处理框架
- 定义业务相关的自定义异常类
- 提供异常处理装饰器和重试机制
- 支持错误上下文管理和优雅恢复

#### `models.py` - 数据模型定义
- 使用dataclass定义标准数据结构
- 自动识别平台类型和提取资源信息
- 支持新旧格式的向后兼容

#### `performance.py` - 性能优化模块
- 提供内存缓存和文件缓存系统
- 支持性能监控和统计分析
- 包含批处理器和计时装饰器

### 业务逻辑模块

#### `extract_resources.py` - 资源提取模块
- 从文本文件中提取影视资源标题和链接
- 支持关键词过滤和标题验证
- 输出标准化的资源数据结构

#### `test_kdocs_fetch.py` - 金山文档获取模块
- 从金山文档网页获取内容
- 支持多种内容提取策略
- 自动识别平台类型和解析链接

#### `tg_forward.py` - Telegram转发模块
- 将资源批量转发到Telegram频道
- 支持频率限制和错误重试
- 提供详细的发送状态统计

#### `deduplication.py` - 去重模块
- 基于标题+链接的智能去重算法
- 自动获取当天频道已发资源进行对比
- 支持本地缓存和持久化存储
- 可配置启用/禁用去重功能

### 主控制脚本

#### `pipeline.py` - 管道协调器
- 协调三个模块的执行流程
- 支持全流程和单步骤运行模式
- 提供统一的命令行接口

## 🚀 使用方法

### 环境配置

1. 复制环境变量配置文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，配置必要参数：
- Telegram API配置
- 目标频道设置
- 金山文档URL
- 过滤关键词等

### 运行方式

#### 使用管道脚本（推荐）

```bash
# 查看帮助
python pipeline.py --help

# 运行完整流程
python pipeline.py full

# 只获取内容
python pipeline.py fetch --url https://www.kdocs.cn/l/example

# 只提取资源
python pipeline.py extract --input debug_content.txt --output results.txt

# 只转发到Telegram
python pipeline.py forward --resource-file results.txt.json

# 自定义过滤关键词
python pipeline.py full --filter-keywords 短剧,软件,广告
```

#### 独立运行模块

```bash
# 资源提取
python extract_resources.py -i input.txt -o output.txt -f 短剧,软件

# 金山文档获取
python test_kdocs_fetch.py

# Telegram转发
python tg_forward.py
```

## 📊 性能特性

- **高速处理**：资源提取平均耗时仅0.001秒
- **智能缓存**：网络请求缓存提升100%性能
- **内存优化**：支持内存和文件双重缓存机制
- **批量处理**：支持大量资源的高效处理
- **智能去重**：基于标题+链接自动去重，避免重复发送

## 🔧 配置选项

### 文件路径配置
- `INPUT_FILE`: 输入文件路径
- `OUTPUT_FILE`: 输出文件路径
- `RESOURCE_FILE`: 资源JSON文件路径

### 资源提取配置
- `FILTER_KEYWORDS`: 过滤关键词（逗号分隔）
- `MIN_TITLE_LENGTH`: 最小标题长度
- `MAX_TITLE_LENGTH`: 最大标题长度

### Telegram配置
- `TG_API_ID`: Telegram API ID
- `TG_API_HASH`: Telegram API Hash
- `TG_STRING_SESSION`: Telegram会话字符串
- `TARGET_CHANNEL`: 目标频道
- `SEND_DELAY`: 发送延迟（秒）

### 金山文档配置
- `KDOCS_URL`: 金山文档URL
- `REQUEST_TIMEOUT`: 请求超时时间
- `USER_AGENT`: 用户代理字符串

### 去重功能配置
- `ENABLE_DEDUPLICATION`: 是否启用去重功能
- `DEDUP_CACHE_FILE`: 去重缓存文件路径
- `DEDUP_DAYS_TO_KEEP`: 去重记录保留天数

## 📈 数据格式

### 资源数据结构
```python
@dataclass
class Resource:
    title: str                    # 资源标题
    links: List[ResourceLink]     # 链接列表
    description: Optional[str]    # 描述
    quality: Optional[str]        # 质量（4K、高清等）
    status: Optional[str]         # 状态（更新至第X集）
    year: Optional[int]           # 年份
```

### 链接数据结构
```python
@dataclass
class ResourceLink:
    url: str                      # 链接地址
    platform: PlatformType        # 平台类型
    password: Optional[str]       # 提取密码
```

## 🛠️ 扩展功能

### 性能监控
系统内置性能监控功能，可以查看各模块的执行时间统计：

```bash
python performance_test.py
```

### 缓存管理
支持内存缓存和文件缓存，可以显著提升重复操作的性能。

### 错误处理
完善的异常处理机制，支持自动重试和优雅降级。

## 📝 注意事项

1. 首次运行前请确保配置了正确的环境变量
2. Telegram API需要从官方获取有效的凭据
3. 建议在生产环境中使用文件缓存以提升性能
4. 系统支持向后兼容，可以处理旧格式的数据文件

## 🔄 更新日志

- ✅ 统一配置管理系统
- ✅ 标准化日志输出
- ✅ 完善异常处理机制
- ✅ 数据模型标准化
- ✅ 性能优化和缓存系统
- ✅ 主控制脚本和管道协调
