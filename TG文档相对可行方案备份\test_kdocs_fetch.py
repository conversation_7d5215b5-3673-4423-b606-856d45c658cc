import requests
import re
from bs4 import BeautifulSoup
import random
import time

class KDocsContentTester:
    def __init__(self):
        # URL匹配模式
        self.url_patterns = [
            r'https://pan\.quark\.cn/s/[a-zA-Z0-9]+',
            r'https://pan\.xunlei\.com/s/[a-zA-Z0-9_-]+\?pwd=[a-zA-Z0-9]+#?',
            r'https://pan\.baidu\.com/s/[a-zA-Z0-9_-]+',
            r'https://alipan\.com/s/[a-zA-Z0-9]+',
            r'https://aliyundrive\.com/s/[a-zA-Z0-9]+',
            r'magnet:\?xt=urn:btih:[a-zA-Z0-9]+[^\s]*',
            r'ed2k://\|file\|[^|]+\|\d+\|[A-Fa-f0-9]+\|/?'
        ]

        # 请求头
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }

    def random_wait(self, min_ms, max_ms):
        """随机等待"""
        min_sec = min_ms / 1000
        max_sec = max_ms / 1000
        wait_time = random.uniform(min_sec, max_sec)
        time.sleep(wait_time)

    def identify_platform(self, url):
        """识别网盘平台"""
        platform_map = {
            'pan.quark.cn': '夸克',
            'pan.xunlei.com': '迅雷',
            'pan.baidu.com': '百度',
            'alipan.com': '阿里云',
            'aliyundrive.com': '阿里云',
            '115.com': '115',
            'cloud.189.cn': '天翼',
            'caiyun.139.com': '移动',
            'mypikpak.com': 'PikPak',
            '123pan.com': '123盘',
            'magnet:': '磁力',
            'ed2k:': 'ed2k'
        }

        for key, value in platform_map.items():
            if key in url:
                return value
        return '其他'

    def fetch_kdocs_content(self, url):
        """获取金山文档内容"""
        try:
            self.random_wait(1000, 3000)
            response = requests.get(url, headers=self.headers, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'

            # 保存原始HTML用于调试
            with open('debug_page.html', 'w', encoding='utf-8') as f:
                f.write(response.text)

            soup = BeautifulSoup(response.text, 'html.parser')

            # 尝试多种方式提取内容
            content_methods = [
                # 方法1: 查找特定的内容容器
                lambda: self.extract_from_containers(soup),
                # 方法2: 查找所有链接及其上下文
                lambda: self.extract_from_links(soup),
                # 方法3: 从页面文本中提取
                lambda: soup.get_text()
            ]

            for i, method in enumerate(content_methods, 1):
                try:
                    content = method()
                    if content and len(content.strip()) > 100:  # 确保内容不为空
                        print(f"使用方法 {i} 成功提取内容，长度: {len(content)}")
                        return content
                except Exception as e:
                    print(f"方法 {i} 失败: {e}")
                    continue

            print("所有提取方法都失败了")
            return None

        except Exception as e:
            print(f"获取文档内容失败 {url}: {e}")
            return None

    def extract_from_containers(self, soup):
        """从特定容器中提取内容"""
        # 常见的内容容器选择器
        selectors = [
            'div[class*="content"]',
            'div[class*="document"]',
            'div[class*="editor"]',
            'div[class*="text"]',
            'main',
            'article',
            '.content',
            '.document-content',
            '.editor-content'
        ]

        for selector in selectors:
            elements = soup.select(selector)
            if elements:
                content = '\n'.join([elem.get_text() for elem in elements])
                if len(content.strip()) > 100:
                    return content

        return None

    def extract_from_links(self, soup):
        """从链接及其上下文中提取内容"""
        content_parts = []

        # 查找所有包含网盘链接的元素
        for pattern in self.url_patterns:
            # 在HTML中查找匹配的链接
            matches = re.finditer(pattern, str(soup))
            for match in matches:
                link = match.group(0)
                # 尝试找到包含这个链接的元素
                link_elements = soup.find_all('a', href=re.compile(re.escape(link)))
                for elem in link_elements:
                    # 获取父元素的文本作为上下文
                    parent = elem.parent
                    if parent:
                        context = parent.get_text().strip()
                        if context:
                            content_parts.append(context)

        return '\n'.join(content_parts) if content_parts else None

    def parse_kdocs_content(self, content):
        """解析金山文档内容，提取标题和链接"""
        results = []
        lines = content.strip().split('\n')

        # 预处理：找到所有可能的标题行
        title_candidates = []
        link_lines = []

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                continue

            # 检查是否包含链接
            has_link = any(re.search(pattern, line) for pattern in self.url_patterns)

            if has_link:
                link_lines.append((i, line))
            elif self.is_likely_title(line):
                title_candidates.append((i, line))

        # 为每个链接行找到最近的标题
        for link_line_idx, link_line in link_lines:
            # 提取链接
            links_found = []
            for pattern in self.url_patterns:
                matches = re.findall(pattern, link_line)
                for match in matches:
                    platform = self.identify_platform(match)
                    links_found.append({
                        'url': match,
                        'platform': platform
                    })

            # 找到最近的标题（向前查找）
            best_title = "Unknown"
            min_distance = float('inf')

            for title_idx, title_line in title_candidates:
                if title_idx < link_line_idx:  # 标题应该在链接之前
                    distance = link_line_idx - title_idx
                    if distance < min_distance:
                        min_distance = distance
                        best_title = title_line

            # 如果没找到合适的标题，尝试从附近的行中提取
            if best_title == "Unknown" or min_distance > 10:
                best_title = self.extract_title_from_nearby_lines(lines, link_line_idx)

            # 为每个链接创建结果
            for link in links_found:
                results.append({
                    'title': best_title,
                    'url': link['url'],
                    'platform': link['platform'],
                    'message': f"**{best_title}**\n\n{link['platform']}: {link['url']}"
                })

        return results

    def is_likely_title(self, line):
        """判断一行是否可能是标题"""
        # 过滤掉明显不是标题的行
        if len(line) < 5 or len(line) > 200:
            return False

        # 排除包含特定词汇的行
        exclude_words = ['搜索方法', '注意', '红包', '优惠', '登录', '点击', '输入', '获取']
        if any(word in line for word in exclude_words):
            return False

        # 包含影视相关关键词的更可能是标题
        title_keywords = ['4K', '高清', '更新至', '第', '季', '集', '电影', '电视剧', '动漫', '高码', '蓝光', '超前']
        if any(keyword in line for keyword in title_keywords):
            return True

        # 包含年份的可能是标题
        if re.search(r'\[?\d{4}\]?', line):
            return True

        # 包含中文且长度适中的可能是标题
        if re.search(r'[\u4e00-\u9fff]', line) and 5 <= len(line) <= 100:
            return True

        return False

    def extract_title_from_nearby_lines(self, lines, current_index):
        """从附近的行中提取标题"""
        # 向前查找标题（最多查找10行）
        for i in range(max(0, current_index - 10), current_index):
            line = lines[i].strip()
            if self.is_likely_title(line):
                return line

        # 如果向前没找到，向后查找（最多查找3行）
        for i in range(current_index + 1, min(len(lines), current_index + 4)):
            line = lines[i].strip()
            if self.is_likely_title(line):
                return line

        return "Unknown"

def test_fetch_content():
    """测试金山文档内容获取"""

    # 创建测试实例
    tester = KDocsContentTester()

    # 测试URL
    test_url = "https://www.kdocs.cn/l/caRody46qOEl"

    print(f"正在获取页面内容: {test_url}")
    content = tester.fetch_kdocs_content(test_url)

    if content:
        print(f"成功获取内容，长度: {len(content)}")
        print("前500个字符:")
        print(content[:500])
        print("\n" + "="*50 + "\n")

        # 测试解析
        print("开始解析资源...")
        resources = tester.parse_kdocs_content(content)

        print(f"解析到 {len(resources)} 个资源:")
        for i, resource in enumerate(resources[:10], 1):  # 只显示前10个
            print(f"{i}. 【{resource['platform']}】{resource['title']}")
            print(f"   链接: {resource['url']}")
            print()

        # 保存解析结果用于调试
        with open('debug_content.txt', 'w', encoding='utf-8') as f:
            f.write(content)

        print("原始内容已保存到 debug_content.txt")
        print("原始HTML已保存到 debug_page.html")

    else:
        print("获取内容失败")

if __name__ == "__main__":
    test_fetch_content()
