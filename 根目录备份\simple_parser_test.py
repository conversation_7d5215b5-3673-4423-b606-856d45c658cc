import re

# 测试数据，基于我们之前看到的页面内容
test_content = """
星际宝贝史迪奇 [2025][4K高码][美国 喜剧 动作 科幻 奇幻 冒险]
夸克: https://pan.quark.cn/s/0cee61ca1243
迅雷: https://pan.xunlei.com/s/VOVk-CFGkWxAS5frRzTMDU7CA1?pwd=xxf8#

星辰变 第六季 4K【超前全28集】已全4K
夸克: https://pan.quark.cn/s/d54c11a36186
迅雷: https://pan.xunlei.com/s/VOIUytg6bHqcVJ9e-KJklRMCA1?pwd=pu4p#

梅根2.0 (2025)[4K高码][英美 科幻 惊悚 恐怖]
夸克: https://pan.quark.cn/s/e11991d2ac07
迅雷: https://pan.xunlei.com/s/VOUn2-odTwpxJeVz1pyJJIDwA1?pwd=keuv#

新·驯龙高手 [2025][4K高码][附全系列][美国 喜剧 动作 奇幻 冒险]
夸克: https://pan.quark.cn/s/33f83c5e074a
迅雷: https://pan.xunlei.com/s/VOU3aqlvv7byryBac8wfIZ_sA1?pwd=ge6v#

锦绣芳华 4K【超前全24集】
夸克: https://pan.quark.cn/s/83707d5426c2
迅雷: https://pan.xunlei.com/s/VOT_zNve5XQQgc6ZqaP-qNmLA1?pwd=uxh4#
"""

class SimpleParser:
    def __init__(self):
        self.url_patterns = [
            r'https://pan\.quark\.cn/s/[a-zA-Z0-9]+',
            r'https://pan\.xunlei\.com/s/[a-zA-Z0-9_-]+\?pwd=[a-zA-Z0-9]+#?',
            r'https://pan\.baidu\.com/s/[a-zA-Z0-9_-]+',
        ]
    
    def parse_content(self, content):
        """解析内容，提取标题和链接"""
        results = []
        lines = content.strip().split('\n')
        
        current_title = ""
        current_links = []
        
        for line in lines:
            line = line.strip()
            if not line:
                # 空行，保存当前资源
                if current_title and current_links:
                    for link in current_links:
                        results.append({
                            'title': current_title,
                            'url': link['url'],
                            'platform': link['platform']
                        })
                current_title = ""
                current_links = []
                continue
            
            # 检查是否包含链接
            has_link = False
            for pattern in self.url_patterns:
                matches = re.findall(pattern, line)
                if matches:
                    has_link = True
                    for match in matches:
                        platform = self.identify_platform(match)
                        current_links.append({
                            'url': match,
                            'platform': platform
                        })
            
            # 如果没有链接，可能是标题
            if not has_link and len(line) > 5:
                # 移除平台标识（如"夸克:"、"迅雷:"）
                clean_line = re.sub(r'^(夸克|迅雷|百度|阿里云|115):\s*', '', line)
                if clean_line and not current_title:
                    current_title = clean_line
        
        # 处理最后一组
        if current_title and current_links:
            for link in current_links:
                results.append({
                    'title': current_title,
                    'url': link['url'],
                    'platform': link['platform']
                })
        
        return results
    
    def identify_platform(self, url):
        """识别网盘平台"""
        if 'pan.quark.cn' in url:
            return '夸克'
        elif 'pan.xunlei.com' in url:
            return '迅雷'
        elif 'pan.baidu.com' in url:
            return '百度'
        elif 'alipan.com' in url or 'aliyundrive.com' in url:
            return '阿里云'
        elif '115.com' in url:
            return '115'
        return '其他'

def test_simple_parser():
    parser = SimpleParser()
    results = parser.parse_content(test_content)
    
    print(f"找到 {len(results)} 个资源链接:")
    for i, item in enumerate(results, 1):
        print(f"{i}. 【{item['platform']}】{item['title']}")
        print(f"   链接: {item['url']}")
        print()
    
    # 分类统计
    platforms = {}
    for item in results:
        platform = item['platform']
        platforms[platform] = platforms.get(platform, 0) + 1
    
    print("分类统计:")
    for platform, count in platforms.items():
        print(f"  {platform}: {count} 个")

if __name__ == "__main__":
    test_simple_parser()
