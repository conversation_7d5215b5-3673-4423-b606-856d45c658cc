#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主控制脚本 - 协调三个模块的执行
支持全流程和单步骤模式，提供统一命令行接口
"""

import sys
import argparse
import asyncio
from pathlib import Path
from typing import Optional, List
from config import config
from logger import get_logger, setup_logging
from exceptions import (
    ConfigurationError,
    FileProcessingError,
    ResourceExtractionError,
    TelegramError,
    ErrorContext
)

# 获取模块日志器
logger = get_logger(__name__)

class ResourcePipeline:
    """资源处理管道"""
    
    def __init__(self):
        self.config = config
        self.logger = logger
    
    def fetch_content(self, url: Optional[str] = None) -> bool:
        """
        步骤1: 从金山文档获取内容
        
        Args:
            url: 金山文档URL，如果为None则使用配置中的URL
            
        Returns:
            bool: 是否成功
        """
        try:
            from test_kdocs_fetch import KDocsContentTester
            
            target_url = url or self.config.kdocs_url
            self.logger.info(f"开始获取金山文档内容: {target_url}")
            
            with ErrorContext("获取金山文档内容", logger_name=__name__) as ctx:
                tester = KDocsContentTester()
                content = tester.fetch_kdocs_content(target_url)
                
                if not content:
                    self.logger.error("未能获取到有效内容")
                    return False
                
                # 保存内容到调试文件
                with open(self.config.debug_content_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.logger.info(f"成功获取内容，长度: {len(content)}，已保存到 {self.config.debug_content_file}")
            
            return ctx.exception is None
            
        except Exception as e:
            self.logger.error(f"获取内容失败: {str(e)}", exc_info=True)
            return False
    
    def extract_resources(self, 
                         input_file: Optional[str] = None,
                         output_file: Optional[str] = None,
                         filter_keywords: Optional[List[str]] = None) -> bool:
        """
        步骤2: 提取和过滤资源
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径
            filter_keywords: 过滤关键词列表
            
        Returns:
            bool: 是否成功
        """
        try:
            from extract_resources import extract_resources, save_to_file, export_json
            
            input_path = input_file or self.config.input_file
            output_path = output_file or self.config.output_file
            keywords = filter_keywords or self.config.filter_keywords
            
            self.logger.info(f"开始提取资源: {input_path} -> {output_path}")
            self.logger.info(f"使用过滤关键词: {', '.join(keywords)}")
            
            with ErrorContext("提取资源", logger_name=__name__) as ctx:
                extraction_result = extract_resources(input_path, keywords)

                if not extraction_result.resources:
                    self.logger.warning("没有提取到有效资源")
                    return True  # 这可能是正常情况

                # 保存结果
                save_to_file(extraction_result, output_path)

                # 导出JSON
                if self.config.export_json:
                    json_file = output_path + ".json"
                    export_json(extraction_result, json_file)

                self.logger.info(f"成功提取 {len(extraction_result.resources)} 个资源项")
            
            return ctx.exception is None
            
        except Exception as e:
            self.logger.error(f"提取资源失败: {str(e)}", exc_info=True)
            return False
    
    async def forward_to_telegram(self, resource_file: Optional[str] = None) -> bool:
        """
        步骤3: 转发到Telegram频道
        
        Args:
            resource_file: 资源文件路径
            
        Returns:
            bool: 是否成功
        """
        try:
            # 动态导入以避免循环导入
            from tg_forward import load_resources, format_message, process_channel_identifier
            from telethon import TelegramClient
            from telethon.sessions import StringSession
            from telethon.errors import FloodWaitError
            from deduplication import TelegramDeduplicator
            
            resource_path = resource_file or self.config.resource_file
            
            self.logger.info(f"开始转发到Telegram: {resource_path}")
            
            # 获取配置
            try:
                api_id, api_hash, string_session, target_channel = self.config.get_required_tg_config()
            except ValueError as e:
                self.logger.warning(f"Telegram配置不完整: {str(e)}")
                self.logger.info("跳过Telegram转发步骤")
                return True  # 配置不完整时跳过，不算失败
            
            # 读取资源
            try:
                resources = load_resources(resource_path)
                if not resources:
                    self.logger.error(f"没有找到资源或资源文件 {resource_path} 为空")
                    return False
            except Exception as e:
                self.logger.error(f"读取资源文件失败: {str(e)}")
                return False
            
            # 处理频道标识符
            channel_identifier = process_channel_identifier(target_channel)
            
            self.logger.info(f"找到 {len(resources)} 个资源项准备转发到频道: {target_channel}")
            
            # 初始化Telegram客户端
            with ErrorContext("Telegram转发", logger_name=__name__) as ctx:
                client = TelegramClient(StringSession(string_session), int(api_id), api_hash)
                await client.start()
                
                try:
                    # 获取频道信息
                    entity = await client.get_entity(channel_identifier)
                    entity_name = getattr(entity, 'title', getattr(entity, 'username', str(channel_identifier)))
                    self.logger.info(f"成功连接到频道: {entity_name}")
                except Exception as e:
                    self.logger.warning(f"获取频道信息失败，将直接使用标识符: {str(e)}")
                    entity = channel_identifier

                # 去重处理
                unique_resources = resources
                duplicate_resources = []
                deduplicator = None

                if self.config.enable_deduplication:
                    self.logger.info("启用去重功能")
                    deduplicator = TelegramDeduplicator(client, entity)
                    unique_resources, duplicate_resources = await deduplicator.filter_duplicates(resources)

                    if duplicate_resources:
                        self.logger.info(f"过滤掉 {len(duplicate_resources)} 个重复资源")

                    if not unique_resources:
                        self.logger.info("所有资源都是重复的，无需发送")
                        await client.disconnect()
                        return True

                    self.logger.info(f"准备发送 {len(unique_resources)} 个新资源")
                else:
                    self.logger.info("去重功能已禁用，将发送所有资源")
                
                # 发送消息
                success_count = 0
                failed_count = 0
                total_resources = len(unique_resources)

                for i, resource in enumerate(unique_resources, 1):
                    message = format_message(resource)
                    title = resource.get('title', '未知标题')
                    links = resource.get('links', [])

                    try:
                        sent_message = await client.send_message(entity, message, parse_mode='md')
                        self.logger.info(f"[{i}/{total_resources}] 成功发送: {title[:50]}...")
                        success_count += 1

                        # 记录已发送的资源（如果启用去重）
                        if deduplicator:
                            deduplicator.mark_as_sent(title, links, sent_message.id)

                        # 延迟避免触发限制
                        if i < total_resources:
                            await asyncio.sleep(self.config.send_delay)

                    except FloodWaitError as e:
                        wait_time = e.seconds
                        self.logger.warning(f"触发频率限制，需等待 {wait_time} 秒")
                        await asyncio.sleep(wait_time)

                        # 重试发送
                        try:
                            sent_message = await client.send_message(entity, message, parse_mode='md')
                            self.logger.info(f"[{i}/{total_resources}] (重试)成功发送: {title[:50]}...")
                            success_count += 1

                            # 记录已发送的资源（如果启用去重）
                            if deduplicator:
                                deduplicator.mark_as_sent(title, links, sent_message.id)
                        except Exception as retry_e:
                            self.logger.error(f"[{i}/{total_resources}] 重试发送失败: {str(retry_e)}")
                            failed_count += 1

                    except Exception as e:
                        self.logger.error(f"[{i}/{total_resources}] 发送消息失败: {str(e)}")
                        failed_count += 1
                
                # 发送完成统计
                original_count = len(resources)
                duplicate_count = len(duplicate_resources)

                if failed_count == 0:
                    self.logger.info(f"🎉 转发完成！")
                    self.logger.info(f"  - 原始资源: {original_count} 个")
                    self.logger.info(f"  - 重复过滤: {duplicate_count} 个")
                    self.logger.info(f"  - 成功发送: {success_count} 个")
                else:
                    self.logger.warning(f"转发完成！")
                    self.logger.warning(f"  - 原始资源: {original_count} 个")
                    self.logger.warning(f"  - 重复过滤: {duplicate_count} 个")
                    self.logger.warning(f"  - 成功发送: {success_count} 个")
                    self.logger.warning(f"  - 发送失败: {failed_count} 个")

                # 显示去重统计（如果启用去重）
                if deduplicator:
                    dedup_stats = deduplicator.get_stats()
                    self.logger.info(f"📊 去重统计: 今日已发送 {dedup_stats['total_sent_today']} 个资源")

                await client.disconnect()
            
            return ctx.exception is None and failed_count == 0
            
        except Exception as e:
            self.logger.error(f"Telegram转发失败: {str(e)}", exc_info=True)
            return False
    
    async def run_full_pipeline(self, 
                               kdocs_url: Optional[str] = None,
                               filter_keywords: Optional[List[str]] = None) -> bool:
        """
        运行完整管道
        
        Args:
            kdocs_url: 金山文档URL
            filter_keywords: 过滤关键词
            
        Returns:
            bool: 是否全部成功
        """
        self.logger.info("开始运行完整资源处理管道")
        
        # 步骤1: 获取内容
        if not self.fetch_content(kdocs_url):
            self.logger.error("步骤1失败: 获取内容失败")
            return False
        
        # 步骤2: 提取资源
        if not self.extract_resources(filter_keywords=filter_keywords):
            self.logger.error("步骤2失败: 提取资源失败")
            return False
        
        # 步骤3: 转发到Telegram
        if not await self.forward_to_telegram():
            self.logger.error("步骤3失败: Telegram转发失败")
            return False
        
        self.logger.info("🎉 完整管道执行成功！")
        return True


def create_parser() -> argparse.ArgumentParser:
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="资源处理管道 - 从金山文档获取、提取、转发资源到Telegram",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 运行完整管道
  python pipeline.py full
  
  # 只获取内容
  python pipeline.py fetch --url https://www.kdocs.cn/l/example
  
  # 只提取资源
  python pipeline.py extract --input debug_content.txt --output results.txt
  
  # 只转发到Telegram
  python pipeline.py forward --resource-file results.txt.json
  
  # 自定义过滤关键词
  python pipeline.py full --filter-keywords 短剧,软件,广告
        """
    )
    
    # 添加子命令
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 完整管道命令
    full_parser = subparsers.add_parser('full', help='运行完整管道')
    full_parser.add_argument('--url', help='金山文档URL')
    full_parser.add_argument('--filter-keywords', help='过滤关键词，用逗号分隔')
    
    # 获取内容命令
    fetch_parser = subparsers.add_parser('fetch', help='从金山文档获取内容')
    fetch_parser.add_argument('--url', help='金山文档URL')
    
    # 提取资源命令
    extract_parser = subparsers.add_parser('extract', help='提取和过滤资源')
    extract_parser.add_argument('--input', help='输入文件路径')
    extract_parser.add_argument('--output', help='输出文件路径')
    extract_parser.add_argument('--filter-keywords', help='过滤关键词，用逗号分隔')
    
    # 转发命令
    forward_parser = subparsers.add_parser('forward', help='转发到Telegram频道')
    forward_parser.add_argument('--resource-file', help='资源文件路径')
    
    # 全局选项
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], 
                       default='INFO', help='日志级别')
    parser.add_argument('--log-file', help='日志文件路径')
    
    return parser


async def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()

    # 如果没有提供命令，显示帮助
    if not args.command:
        parser.print_help()
        return

    # 设置日志
    setup_logging(
        log_level=args.log_level,
        log_file=args.log_file
    )

    logger.info(f"开始执行命令: {args.command}")

    # 创建管道实例
    pipeline = ResourcePipeline()

    try:
        success = False

        if args.command == 'full':
            # 运行完整管道
            filter_keywords = None
            if args.filter_keywords:
                filter_keywords = [kw.strip() for kw in args.filter_keywords.split(',')]

            success = await pipeline.run_full_pipeline(
                kdocs_url=args.url,
                filter_keywords=filter_keywords
            )

        elif args.command == 'fetch':
            # 只获取内容
            success = pipeline.fetch_content(args.url)

        elif args.command == 'extract':
            # 只提取资源
            filter_keywords = None
            if args.filter_keywords:
                filter_keywords = [kw.strip() for kw in args.filter_keywords.split(',')]

            success = pipeline.extract_resources(
                input_file=args.input,
                output_file=args.output,
                filter_keywords=filter_keywords
            )

        elif args.command == 'forward':
            # 只转发到Telegram
            success = await pipeline.forward_to_telegram(args.resource_file)

        # 根据结果退出
        if success:
            logger.info("✅ 命令执行成功")
            sys.exit(0)
        else:
            logger.error("❌ 命令执行失败")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("用户中断操作")
        sys.exit(0)
    except Exception as e:
        logger.error(f"执行过程中发生未预期的错误: {str(e)}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
