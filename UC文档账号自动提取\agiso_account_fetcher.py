#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
阿奇索UC账号密码获取脚本
定时从阿奇索自动提货系统获取UC浏览器VIP账号密码信息
"""

import asyncio
import json
import re
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('agiso_fetcher.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class AgisoAccountFetcher:
    def __init__(self, output_file='agiso_account.json'):
        self.url = "https://aldsidle.agiso.com/tq/#/extract?path=ezyrhv&tid=2849563706251031279"
        self.output_file = Path(output_file)
        
    async def fetch_account_info(self):
        """获取UC账号信息"""
        try:
            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 设置用户代理
                await page.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                
                logger.info(f"正在访问页面: {self.url}")
                await page.goto(self.url, wait_until='networkidle')
                
                # 等待页面内容加载完成
                await page.wait_for_timeout(8000)  # 阿奇索页面需要更长加载时间
                
                # 获取页面内容
                content = await page.content()
                
                # 提取账号信息
                account_info = await self._extract_account_info(page, content)
                
                await browser.close()
                return account_info
                
        except Exception as e:
            logger.error(f"获取账号信息失败: {str(e)}")
            return None

    async def _extract_account_info(self, page, content):
        """从阿奇索页面内容中提取账号信息"""
        try:
            # 获取页面纯文本内容
            text_content = await page.inner_text('body')
            
            logger.info(f"页面内容长度: {len(content)}")
            logger.info(f"纯文本内容长度: {len(text_content)}")
            
            # 调试：保存内容到文件
            with open('debug_agiso_content.html', 'w', encoding='utf-8') as f:
                f.write(content)
            with open('debug_agiso_text.txt', 'w', encoding='utf-8') as f:
                f.write(text_content)
            
            account = None
            password = None
            
            # 方法1: 查找"卡:"和"密:"标识的内容
            # 在阿奇索页面中，账号密码格式为：
            # 卡: ************
            # 密: zxc666
            
            # 查找卡号
            card_pattern = r'卡[:\s]*([0-9]+)'
            card_match = re.search(card_pattern, text_content)
            if card_match:
                account = card_match.group(1).strip()
                logger.info(f"找到账号: {account}")
            
            # 查找密码
            password_pattern = r'密[:\s]*([a-zA-Z0-9]+)'
            password_match = re.search(password_pattern, text_content)
            if password_match:
                password = password_match.group(1).strip()
                logger.info(f"找到密码: {password}")
            
            # 方法2: 如果上面没找到，尝试通过页面元素定位
            if not account or not password:
                try:
                    # 查找包含账号的元素
                    account_elements = await page.query_selector_all('text=/^[0-9]{10,15}$/')
                    for element in account_elements:
                        text = await element.inner_text()
                        if text and text.isdigit() and len(text) >= 10:
                            account = text.strip()
                            logger.info(f"通过元素找到账号: {account}")
                            break
                    
                    # 查找包含密码的元素（字母数字组合）
                    password_elements = await page.query_selector_all('text=/^[a-zA-Z0-9]{3,20}$/')
                    for element in password_elements:
                        text = await element.inner_text()
                        if text and re.match(r'^[a-zA-Z0-9]{3,20}$', text) and not text.isdigit():
                            password = text.strip()
                            logger.info(f"通过元素找到密码: {password}")
                            break
                            
                except Exception as e:
                    logger.warning(f"通过元素定位失败: {str(e)}")
            
            # 获取更新时间
            update_time = await self._extract_update_time(page, text_content)
            
            if account and password:
                account_info = {
                    'account': account,
                    'password': password,
                    'update_time': update_time,
                    'fetch_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'source_url': self.url,
                    'source_type': 'agiso'
                }
                
                logger.info(f"成功获取账号信息: {account} / {password}")
                return account_info
            else:
                logger.warning("未能提取到完整的账号密码信息")
                logger.warning(f"账号: {account}, 密码: {password}")
                return None
                
        except Exception as e:
            logger.error(f"提取账号信息时出错: {str(e)}")
            return None
    
    async def _extract_update_time(self, page, text_content):
        """提取更新时间"""
        try:
            # 阿奇索页面的时间格式：付款时间：2025-07-26 17:56:00
            time_patterns = [
                r'付款时间[：:]\s*(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2}:\d{2})',
                r'付款时间[：:]\s*(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2})',
                r'(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2}:\d{2})',
                r'(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2})'
            ]
            
            for pattern in time_patterns:
                match = re.search(pattern, text_content)
                if match:
                    return match.group(1)
            
            return "未知时间"
            
        except Exception as e:
            logger.warning(f"提取更新时间失败: {str(e)}")
            return "提取失败"

    def save_to_file(self, account_info):
        """保存账号信息到文件"""
        try:
            # 保存为JSON格式
            with open(self.output_file, 'w', encoding='utf-8') as f:
                json.dump(account_info, f, ensure_ascii=False, indent=2)
            
            # 保存为TXT格式
            txt_file = self.output_file.with_suffix('.txt')
            with open(txt_file, 'w', encoding='utf-8') as f:
                f.write(f"UC浏览器VIP账号信息 (阿奇索源)\n")
                f.write(f"获取时间: {account_info['fetch_time']}\n")
                f.write(f"更新时间: {account_info['update_time']}\n")
                f.write(f"账号: {account_info['account']}\n")
                f.write(f"密码: {account_info['password']}\n")
                f.write(f"来源: {account_info['source_url']}\n")
            
            logger.info(f"账号信息已保存到: {self.output_file} 和 {txt_file}")
            return True
            
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            return False

    async def run(self):
        """运行获取流程"""
        logger.info("开始获取阿奇索UC账号信息...")
        
        account_info = await self.fetch_account_info()
        
        if account_info:
            success = self.save_to_file(account_info)
            if success:
                logger.info("任务完成!")
                return account_info
            else:
                logger.error("保存失败!")
                return None
        else:
            logger.error("获取账号信息失败!")
            return None

async def main():
    """主函数"""
    fetcher = AgisoAccountFetcher()
    result = await fetcher.run()
    
    if result:
        print(f"\n获取成功:")
        print(f"账号: {result['account']}")
        print(f"密码: {result['password']}")
        print(f"更新时间: {result['update_time']}")
        print(f"来源: 阿奇索系统")
    else:
        print("获取失败，请查看日志文件")

if __name__ == "__main__":
    asyncio.run(main())
