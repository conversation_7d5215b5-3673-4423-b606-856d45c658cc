#!/usr/bin/env python3
"""
配置验证脚本
"""

import os
from pathlib import Path
from config import Config

def validate_env_file():
    """验证 .env 文件"""
    env_file = Path(__file__).parent / '.env'
    
    print("🔍 检查配置文件...")
    print(f"📁 配置文件路径: {env_file}")
    
    if not env_file.exists():
        print("❌ .env 文件不存在")
        print("💡 请运行: cp .env.example .env")
        return False
    
    print("✅ .env 文件存在")
    
    # 检查必要配置
    required_configs = {
        'TG_API_ID': Config.TG_API_ID,
        'TG_API_HASH': Config.TG_API_HASH,
        'TG_STRING_SESSION': Config.TG_STRING_SESSION,
        'TARGET_CHANNEL': Config.TARGET_CHANNEL,
    }
    
    print("\n🔧 检查必要配置:")
    all_valid = True
    
    for key, value in required_configs.items():
        if not value or (isinstance(value, int) and value == 0):
            print(f"❌ {key}: 未配置")
            all_valid = False
        else:
            # 隐藏敏感信息
            if key in ['TG_API_HASH', 'TG_STRING_SESSION']:
                display_value = f"{str(value)[:10]}..." if len(str(value)) > 10 else "已配置"
            else:
                display_value = str(value)
            print(f"✅ {key}: {display_value}")
    
    # 检查可选配置
    optional_configs = {
        'KDOCS_URL': Config.KDOCS_URL,
        'HEADLESS': Config.HEADLESS,
        'DELAY_BETWEEN_REQUESTS': Config.DELAY_BETWEEN_REQUESTS,
        'MAX_RETRIES': Config.MAX_RETRIES,
        'ENABLE_CLASSIFICATION': Config.ENABLE_CLASSIFICATION,
        'MIN_CONTENT_LENGTH': Config.MIN_CONTENT_LENGTH,
    }
    
    print("\n⚙️ 可选配置:")
    for key, value in optional_configs.items():
        print(f"  {key}: {value}")
    
    return all_valid

def validate_patterns():
    """验证模式配置"""
    print("\n🔍 检查模式配置...")
    
    print(f"📋 分类模式数量: {len(Config.CATEGORY_PATTERNS)}")
    for i, pattern in enumerate(Config.CATEGORY_PATTERNS, 1):
        print(f"  {i}. {pattern}")
    
    print(f"\n🗑️ 噪音模式数量: {len(Config.NOISE_PATTERNS)}")
    for i, pattern in enumerate(Config.NOISE_PATTERNS, 1):
        print(f"  {i}. {pattern}")
    
    print(f"\n🎯 选择器数量: {len(Config.SCRAPER_SELECTORS)}")
    for i, selector in enumerate(Config.SCRAPER_SELECTORS, 1):
        print(f"  {i}. {selector}")

def test_config_validation():
    """测试配置验证"""
    print("\n🧪 测试配置验证...")
    
    try:
        Config.validate()
        print("✅ 配置验证通过")
        return True
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 TG转发文档版 - 配置验证")
    print("=" * 50)
    
    # 验证环境文件
    env_valid = validate_env_file()
    
    # 验证模式配置
    validate_patterns()
    
    # 测试配置验证
    config_valid = test_config_validation()
    
    print("\n" + "=" * 50)
    print("📊 验证结果:")
    print(f"  环境配置: {'✅ 通过' if env_valid else '❌ 失败'}")
    print(f"  配置验证: {'✅ 通过' if config_valid else '❌ 失败'}")
    
    if env_valid and config_valid:
        print("\n🎉 所有配置验证通过，可以运行程序！")
        print("🚀 运行命令: python main.py")
    else:
        print("\n⚠️ 配置存在问题，请修复后再运行")
        if not env_valid:
            print("💡 请检查并完善 .env 文件配置")
        if not config_valid:
            print("💡 请确保必要的配置项都已正确设置")

if __name__ == "__main__":
    main()
