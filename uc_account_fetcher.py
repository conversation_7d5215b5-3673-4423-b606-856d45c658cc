#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UC账号密码获取脚本
定时从语雀页面获取UC浏览器VIP账号密码信息
"""

import asyncio
import json
import re
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('uc_fetcher.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class UCAccountFetcher:
    def __init__(self, output_file='uc_account.json'):
        self.url = "https://www.yuque.com/ganjuezijitaihaole/ovcqrm/ni0t2qna08dnw2q7?singleDoc#"
        self.output_file = Path(output_file)
        
    async def fetch_account_info(self):
        """获取UC账号信息"""
        try:
            async with async_playwright() as p:
                # 启动浏览器
                browser = await p.chromium.launch(headless=True)
                page = await browser.new_page()
                
                # 设置用户代理
                await page.set_extra_http_headers({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
                
                logger.info(f"正在访问页面: {self.url}")
                await page.goto(self.url, wait_until='networkidle')
                
                # 等待页面内容加载
                await page.wait_for_timeout(3000)
                
                # 获取页面内容
                content = await page.content()
                
                # 提取账号信息
                account_info = await self._extract_account_info(page, content)
                
                await browser.close()
                return account_info
                
        except Exception as e:
            logger.error(f"获取账号信息失败: {str(e)}")
            return None
    
    async def _extract_account_info(self, page, content):
        """从页面内容中提取账号信息"""
        try:
            # 方法1: 通过文本内容匹配
            account_pattern = r'"(\d{12})"'  # 匹配12位数字账号
            password_pattern = r'uc\d+'      # 匹配uc开头的密码
            
            account_match = re.search(account_pattern, content)
            password_match = re.search(password_pattern, content)
            
            account = account_match.group(1) if account_match else None
            password = password_match.group(0) if password_match else None
            
            # 方法2: 尝试通过页面元素获取
            if not account or not password:
                try:
                    # 获取所有文本内容
                    text_content = await page.inner_text('body')
                    
                    # 重新匹配
                    account_match = re.search(r'(\d{12})', text_content)
                    password_match = re.search(r'uc\d+', text_content)
                    
                    if account_match:
                        account = account_match.group(1)
                    if password_match:
                        password = password_match.group(0)
                        
                except Exception as e:
                    logger.warning(f"通过页面元素获取失败: {str(e)}")
            
            # 获取更新时间
            update_time = await self._extract_update_time(page, content)
            
            if account and password:
                account_info = {
                    'account': account,
                    'password': password,
                    'update_time': update_time,
                    'fetch_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'source_url': self.url
                }
                
                logger.info(f"成功获取账号信息: {account} / {password}")
                return account_info
            else:
                logger.warning("未能提取到完整的账号密码信息")
                return None
                
        except Exception as e:
            logger.error(f"提取账号信息时出错: {str(e)}")
            return None
    
    async def _extract_update_time(self, page, content):
        """提取更新时间"""
        try:
            # 尝试多种时间格式匹配
            time_patterns = [
                r'今天\s+(\d{1,2}:\d{2})',
                r'昨天\s+(\d{1,2}:\d{2})',
                r'(\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2})',
                r'(\d{4}-\d{1,2}-\d{1,2}\s+\d{1,2}:\d{2})'
            ]
            
            text_content = await page.inner_text('body')
            
            for pattern in time_patterns:
                match = re.search(pattern, text_content)
                if match:
                    return match.group(0)
            
            return "未知时间"
            
        except Exception as e:
            logger.warning(f"提取更新时间失败: {str(e)}")
            return "提取失败"
    
    def save_to_file(self, account_info):
        """保存账号信息到文件"""
        try:
            if account_info:
                # 保存为JSON格式
                with open(self.output_file, 'w', encoding='utf-8') as f:
                    json.dump(account_info, f, ensure_ascii=False, indent=2)
                
                # 同时保存为纯文本格式便于查看
                txt_file = self.output_file.with_suffix('.txt')
                with open(txt_file, 'w', encoding='utf-8') as f:
                    f.write(f"UC浏览器VIP账号信息\n")
                    f.write(f"=" * 30 + "\n")
                    f.write(f"账号: {account_info['account']}\n")
                    f.write(f"密码: {account_info['password']}\n")
                    f.write(f"页面更新时间: {account_info['update_time']}\n")
                    f.write(f"获取时间: {account_info['fetch_time']}\n")
                    f.write(f"来源: {account_info['source_url']}\n")
                
                logger.info(f"账号信息已保存到: {self.output_file} 和 {txt_file}")
                return True
            else:
                logger.warning("没有有效的账号信息可保存")
                return False
                
        except Exception as e:
            logger.error(f"保存文件失败: {str(e)}")
            return False
    
    async def run(self):
        """运行获取流程"""
        logger.info("开始获取UC账号信息...")
        
        account_info = await self.fetch_account_info()
        
        if account_info:
            success = self.save_to_file(account_info)
            if success:
                logger.info("任务完成!")
                return account_info
            else:
                logger.error("保存失败!")
                return None
        else:
            logger.error("获取账号信息失败!")
            return None

async def main():
    """主函数"""
    fetcher = UCAccountFetcher()
    result = await fetcher.run()
    
    if result:
        print(f"\n获取成功:")
        print(f"账号: {result['account']}")
        print(f"密码: {result['password']}")
        print(f"更新时间: {result['update_time']}")
    else:
        print("获取失败，请查看日志文件")

if __name__ == "__main__":
    asyncio.run(main())
