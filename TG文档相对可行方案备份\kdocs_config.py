from KDocsForwarder import KDocsForwarder

if __name__ == '__main__':
    # 金山文档URL列表
    kdocs_urls = [
        'https://www.kdocs.cn/l/caRody46qOEl',
        # 可以添加更多金山文档链接
    ]
    
    # 转发目标频道
    forward_to_channel = 'tgsearchers2'
    
    # 包含关键词 - 只转发包含这些关键词的资源
    include = ['链接', '片名', '名称', '剧名', 'ed2k','magnet', 'drive.uc.cn', 'caiyun.139.com', 'cloud.189.cn', 
               '123684.com','123685.com','123912.com','123pan.com','123pan.cn','123592.com',
               'pan.quark.cn', '115cdn.com','115.com', 'anxia.com', 'alipan.com', 'aliyundrive.com', 
               '夸克云盘', '阿里云盘', '磁力链接','Alipan','Quark','115','Baidu', '4K', '高清', '更新至', 
               '第', '季', '集', '电影', '电视剧', '动漫']
    
    # 排除关键词 - 不转发包含这些关键词的资源
    exclude = ['小程序', '预告', '预感', '盈利', '即可观看', '书籍', '电子书', '图书', '丛书', '期刊','app','软件', 
               '破解版','解锁','专业版','高级版','最新版','食谱', '免安装', '免广告','安卓', 'Android', '课程', 
               '作品', '教程', '教学', '全书', '名著', 'mobi', 'MOBI', 'epub','任天堂','PC','单机游戏',
               'pdf', 'PDF', 'PPT', '抽奖', '完整版', '有声书','读者','文学', '写作', '节课', '套装', '话术', 
               '纯净版', '日历''txt', 'MP3','网赚', 'mp3', 'WAV', 'CD', '音乐', '专辑', '模板', '书中', '读物', 
               '入门', '零基础', '常识', '电商', '小红书','JPG','短视频','工作总结', '写真','抖音', '资料', 
               '华为', '短剧', '纪录片', '记录片', '纪录', '纪实', '学习', '付费', '小学', '初中','数学', '语文']
    
    # 消息中的超链接文字，如果存在超链接，会用url替换文字
    hyperlink_text = {
        "magnet": ["点击查看","@@"],
        "ed2k": ["点击查看","@@"],
        "uc": ["点击查看","@@"],
        "mobile": ["点击查看","@@"],
        "tianyi": ["点击查看","@@"],
        "quark": ["😀 Quark","【夸克网盘】点击获取","夸克云盘","点击查看","@@"],
        "115": ["😀 115","115云盘","点击查看","@@"],
        "aliyun": ["😀 Alipan","【阿里云盘】点击获取","阿里云盘","点击查看","@@"],
        "pikpak": ["PikPak云盘","点击查看","@@"],
        "baidu": ["😀 Baidu","【百度网盘】点击获取","百度云盘","点击查看","@@"],
        "123": ["点击查看","@@"],
        "others": ["点击查看","@@"],
    }
    
    # 替换消息中关键字(tag/频道/群组)
    replacements = {
        forward_to_channel: ['xlshare','yunpangroup','pan123pan','juziminmao',"yunpanall","NewAliPan","ucquark", 
                             "uckuake", "yunpanshare", "yunpangroup", "Quark_0", "guaguale115", "Aliyundrive_Share_Channel", 
                             "alyd_g", "shareAliyun", "aliyundriveShare", "hao115", "Mbox115", "NewQuark", "Quark_Share_Group", 
                             "QuarkRobot", "memosfanfan_bot",'pankuake_share', "Quark_Movies", "aliyun_share_bot", "AliYunPanBot",
                             "None","大风车","雷锋","热心网友","xx123pan","xx123pan1"],
        "": ['via Hamilton 分享','via 孔 子','🕸源站：https://tv.yydsys.top','via 特别大 爱新觉罗',"🦜投稿", "• ", "🐝", 
             "树洞频道", "云盘投稿", "广告合作", "✈️ 画境频道", "🌐 画境官网", "🎁 详情及下载", " - 影巢", "帮助咨询", 
             "🌈 分享人: 自动发布","分享者：123盘社区","🌥云盘频道 - 📦", "🌍： 群主自用机场: 守候网络, 9折活动!", 
             "🔥： 阿里云盘播放神器: VidHub","🔥： 阿里云盘全能播放神器: VidHub","🔥： 移动云盘免流丝滑挂载播放: VidHub", 
             "画境流媒体播放器-免费看奈飞，迪士尼！",'播放神器: VidHub','🔥： https://www.alipan.com/s/2gk164mf2oN',
             "AIFUN 爱翻 BGP入口极速专线", "AIFUN 爱翻 机场", "from 天翼云盘日更频道","via 匿名","🖼️ 奥斯卡4K蓝光影视站",
             "投稿: 点击投稿","────────────────","【1】需要迅雷云盘链接请进群，我会加入更新",
             "【2】求随手单点频道内容，点赞❤️👍等表情","【3】帮找❗️资源，好片源（别客气）","【4】目前共４个频道，分类内容发布↓",
             "【5】更多请看简介［含™「莫愁片海•拾贝十倍」社群］与🐧/🌏正式群"," - 📌","🚀 频 道: 热剧追更","🔍 群 组: 聚合搜索",
             "💬 公众号: 爱影搜","🌈 分享自: 爱影VIP"]
    }
    
    # 自定义统计置顶消息，markdown格式
    message_md = (
        "**Github：[https://github.com/fish2018](https://github.com/fish2018)**\n\n"
        "**本频道实时更新最新影视资源并自动清理失效链接(123、夸克、阿里云、天翼、UC、115、移动、磁力、百度、迅雷)**\n\n"
        "**推荐播放器：[影视](https://t.me/ys_tvb)**\n\n"
        "**[PG](https://t.me/pandagroovechat)接口：    [备用](https://cnb.cool/fish2018/pg/-/git/raw/master/jsm.json)   [备用2](http://www.fish2018.ip-ddns.com/p/jsm.json)   [备用3](http://www3.fish2018.ip-ddns.com/p/jsm.json) **"
        "```https://www.252035.xyz/p/jsm.json```"
        "**tgsearch服务器(PG)：    [备用](http://tg.fish2018.ip-ddns.com)    [备用2](http://tg3.fish2018.ip-ddns.com)**"
        "```https://tg.252035.xyz```"
        "**[真心](https://t.me/juejijianghuchat)接口：    [备用](https://cnb.cool/fish2018/zx/-/git/raw/master/FongMi.json)   [备用2](http://www.fish2018.ip-ddns.com/z/FongMi.json)   [备用3](http://www3.fish2018.ip-ddns.com/z/FongMi.json) **"
        "```https://www.252035.xyz/z/FongMi.json```"
        "**tgsou服务器(真心)：    [备用](http://tgsou.fish2018.ip-ddns.com)    [备用2](http://tgsou3.fish2018.ip-ddns.com)**"
        "```https://tgsou.252035.xyz```"
        "**在线获取TG Session**"
        "```https://tgs.252035.xyz```"
        "**18x接口：    [备用](https://cnb.cool/fish2018/pg/-/git/raw/master/jsm.json-18x)   [备用2](http://www.fish2018.ip-ddns.com/p/jsm.json?pwd=18x)   [备用3](http://www3.fish2018.ip-ddns.com/p/jsm.json?pwd=18x) **"
        "```https://www.252035.xyz/p/jsm.json?pwd=18x```"
        "**壁纸接口(18x)：   [备用](http://www.fish2018.ip-ddns.com/imgs)   [备用2](http://www3.fish2018.ip-ddns.com/imgs)    [私藏品鉴](https://t.me/sicangpinjian)**"
        "```https://www.252035.xyz/imgs```"
    )
    
    # 匹配关键字分发到不同频道/群组，不需要分发直接设置channel_match=[]即可
    channel_match = []
    
    # TG API 配置
    api_id = 6627460
    api_hash = '27a53a0965e486a2bc1b1fcde473b1c4'
    string_session = 'xxx'  # 请替换为你的session
    
    # 代理设置
    proxy = None
    # proxy = (socks.SOCKS5, '127.0.0.1', 7897)
    
    # 首次检测自己频道最近checknum条消息去重
    checknum = 50
    
    # 允许转发今年之前的资源
    past_years = False
    
    # 只允许转发当日的
    only_today = False
    
    # 检查间隔（秒）- 用于持续监控模式
    check_interval = 3600  # 1小时检查一次
    
    # 创建转发器实例
    forwarder = KDocsForwarder(
        api_id=api_id,
        api_hash=api_hash,
        string_session=string_session,
        kdocs_urls=kdocs_urls,
        forward_to_channel=forward_to_channel,
        include=include,
        exclude=exclude,
        proxy=proxy,
        checknum=checknum,
        replacements=replacements,
        message_md=message_md,
        channel_match=channel_match,
        hyperlink_text=hyperlink_text,
        past_years=past_years,
        only_today=only_today,
        check_interval=check_interval
    )
    
    # 运行模式选择
    # 单次运行
    forwarder.run()
    
    # 持续监控模式（取消注释下面这行，注释上面这行）
    # forwarder.run_continuous_mode()
